# 分数一致性和affectRows问题修复

## 🚨 问题现象
1. **分数不一致**: 这次统计的分数与前几次不一致
2. **affectRows异常**: 返回280而不是预期的192

## 🔍 问题根因分析

### **问题1: 分数计算逻辑变更**
```java
// 原始逻辑（正确）- 基于remark判断
if (ruleDetailVo.getRemark().equals("基础分")) {
    basicScore += finalScore;
}

// 修改后的逻辑（导致不一致）- 基于策略类型判断
if (STRATEGY_TYPE_BASIC.equals(ruleType)) {
    accumulator.addBasicScore(finalScore);
}
```

**问题分析**:
- 虽然基于策略类型的判断在理论上更正确，但改变了现有的分数分类逻辑
- 可能存在策略类型与remark不完全对应的情况
- 导致分数分类发生变化，总分可能相同但基础分/奖励分分配不同

### **问题2: 数据重复或处理逻辑错误**
```java
// 可能的原因：
// 1. 并行处理中的数据重复
// 2. 去重逻辑不完善
// 3. 批处理计数错误
// 4. ON DUPLICATE KEY UPDATE的影响行数计算
```

## ✅ 修复方案

### **修复1: 回退到原始分数计算逻辑**
```java
// 使用修复版的原始计算逻辑
private List<RuleScore> calculateScoreOriginalFixed(List<CommitteeMember> memberList, RuleInfoVo ruleInfo,
                                                   TripleMap<String, String, String> strategyMap, String ruleInfoJson) {
    for (CommitteeMember member : memberList) {
        // 直接使用原始规则信息，避免深拷贝导致的strategyKey丢失
        for (RuleDetailVo ruleDetailVo : ruleInfo.getChildren()) {
            resetFinalScores(ruleDetailVo);
            evaluateLeafNodesOriginal(member, ruleDetailVo, strategyMap);
            
            // 保持原有的基于remark的判断逻辑，确保分数一致性
            if (ruleDetailVo.getRemark().equals("基础分")) {
                basicScore += finalScore != null ? finalScore : 0;
            }
            if (ruleDetailVo.getRemark().equals("奖励分")) {
                rewardScore += finalScore != null ? finalScore : 0;
            }
        }
    }
}
```

**优势**:
- 保持与历史数据的一致性
- 避免因逻辑变更导致的分数差异
- 解决了深拷贝导致的strategyKey丢失问题

### **修复2: 增强去重和监控逻辑**
```java
// 详细的重复数据检测
Map<Long, Integer> duplicateCount = new HashMap<>();
for (RuleScore score : ruleScoreList) {
    if (idSet.add(score.getId())) {
        uniqueList.add(score);
    } else {
        duplicateCount.put(score.getId(), duplicateCount.getOrDefault(score.getId(), 1) + 1);
        log.warn("发现重复的委员ID: {}, 重复次数: {}", score.getId(), duplicateCount.get(score.getId()));
    }
}

// 输出详细的处理信息
log.info("去重后数据量: {} -> {}, 重复委员数: {}", 
        ruleScoreList.size(), uniqueList.size(), duplicateCount.size());
```

### **修复3: 修正finalScore重置逻辑**
```java
// 修复前：重置为0可能影响计算
ruleDetail.setFinalScore(0);

// 修复后：重置为null，让策略方法正确设置
ruleDetail.setFinalScore(null);
```

## 📊 预期修复效果

### **分数一致性**
- ✅ 使用与历史版本相同的分数计算逻辑
- ✅ 基础分和奖励分的分类保持一致
- ✅ 总分应该与之前的计算结果相同

### **affectRows正确性**
- ✅ 应该返回192（等于委员数量）
- ✅ 去重逻辑确保没有重复数据
- ✅ 详细日志帮助诊断任何异常情况

## 🔍 诊断信息

修复后的代码会输出以下关键信息：

### **1. 数据处理统计**
```
开始修复版原始计算逻辑，委员数量: 192
修复版原始计算完成，数据量: 192
```

### **2. 去重检测结果**
```
开始批量保存规则分数，数据量: 192
实际处理数据量: 192
// 如果有重复：
去重后数据量: 280 -> 192, 重复委员数: X
委员ID XXX 重复了 Y 次
```

### **3. 委员详情样本**
```
委员详情: ID=1, 姓名=张三, 基础分=50, 奖励分=10, 总分=60
委员详情: ID=2, 姓名=李四, 基础分=45, 奖励分=15, 总分=60
委员详情: ID=3, 姓名=王五, 基础分=40, 奖励分=20, 总分=60
```

### **4. 批处理结果**
```
分批处理，批次数: 4, 每批大小: 50
批量保存完成，总影响行数: 192
```

## ⚠️ 注意事项

### **1. 分数一致性验证**
- 对比修复前后的分数结果
- 重点检查基础分和奖励分的分配
- 确认总分与历史数据一致

### **2. affectRows异常诊断**
如果affectRows仍然不等于192，检查：
- 日志中是否有重复委员ID的警告
- 数据库中是否存在重复记录
- ON DUPLICATE KEY UPDATE的具体行为

### **3. 性能影响**
- 回退到串行处理可能略微影响性能
- 但确保了数据正确性和一致性
- 可以在验证正确性后再考虑性能优化

## 🎯 验证清单

- [ ] 运行修复后的代码
- [ ] 检查分数是否与历史数据一致
- [ ] 确认affectRows等于192
- [ ] 查看详细的诊断日志
- [ ] 验证数据库中没有重复记录
- [ ] 对比修复前后的具体分数差异

## 📈 后续计划

### **1. 如果分数仍不一致**
- 详细对比具体委员的分数差异
- 检查策略方法的执行结果
- 验证规则配置是否有变化

### **2. 如果affectRows仍异常**
- 分析重复数据的来源
- 检查数据库表结构和约束
- 考虑使用更简单的插入逻辑

### **3. 长期优化**
- 在确保正确性后，逐步引入性能优化
- 考虑基于策略类型的分数分类（需要数据迁移）
- 建立自动化的分数一致性检查机制

## 总结

通过回退到经过验证的原始分数计算逻辑，并增强数据去重和监控机制，应该能够解决分数不一致和affectRows异常的问题。

关键是先确保功能正确性，再考虑性能和架构优化。
