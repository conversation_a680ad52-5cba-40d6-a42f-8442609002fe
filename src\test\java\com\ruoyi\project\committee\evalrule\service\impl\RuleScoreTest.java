package com.ruoyi.project.committee.evalrule.service.impl;


import com.ruoyi.project.committee.evalrule.service.IRuleScoreService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
public class RuleScoreTest {

    @Resource
    private IRuleScoreService ruleScoreService;

    @Test
    public void test() {
        Integer affectRows = ruleScoreService.calculateScore(2024);
        System.out.println("affectRows = " + affectRows);
    }
}
