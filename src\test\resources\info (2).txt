11:45:55.547 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,464] - 开始修复版原始计算逻辑，委员数量: 192
11:45:55.550 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,470] - 开始计算委员: 黄旭
11:45:55.550 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,476] - 处理顶级规则: 基础分
11:45:55.550 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 基础分, 策略键: null, 是否有子节点: true
11:45:55.551 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加会议（以半天计）, 策略键: null, 是否有子节点: true
11:45:55.551 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 缺席区政协全体会议1次（未请假）, 策略键: absence:plenary:unexcused, 是否有子节点: false
11:45:55.551 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: absence:plenary:unexcused
11:45:55.551 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=缺席区政协全体会议1次（未请假）, 子节点分数=0, 累计分数=0
11:45:55.552 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 缺席区政协全体会议1次（请假）, 策略键: absence:plenary:excused, 是否有子节点: false
11:45:55.552 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: absence:plenary:excused
11:45:55.552 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=缺席区政协全体会议1次（请假）, 子节点分数=0, 累计分数=0
11:45:55.552 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 缺席区政协常委会议1次（适用于区政协常委）（未请假）, 策略键: absence:standing:unexcused, 是否有子节点: false
11:45:55.552 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: absence:standing:unexcused
11:45:55.553 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=缺席区政协常委会议1次（适用于区政协常委）（未请假）, 子节点分数=0, 累计分数=0
11:45:55.553 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 缺席区政协常委会议1次（适用于区政协常委）（请假）, 策略键: absence:standing:excused, 是否有子节点: false
11:45:55.553 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: absence:standing:excused
11:45:55.553 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=缺席区政协常委会议1次（适用于区政协常委）（请假）, 子节点分数=0, 累计分数=0
11:45:55.554 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 缺席区政协专门委员会会议1次（未请假）, 策略键: absence:committee:unexcused, 是否有子节点: false
11:45:55.554 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: absence:committee:unexcused
11:45:55.554 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=缺席区政协专门委员会会议1次（未请假）, 子节点分数=0, 累计分数=0
11:45:55.554 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 缺席区政协专门委员会会议1次（请假）, 策略键: absence:committee:excused, 是否有子节点: false
11:45:55.554 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: absence:committee:excused
11:45:55.555 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=缺席区政协专门委员会会议1次（请假）, 子节点分数=0, 累计分数=0
11:45:55.555 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 区政协委员列席区政协常委扩大会议, 策略键: attendance:standing:extended, 是否有子节点: false
11:45:55.555 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: attendance:standing:extended
11:45:55.555 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=区政协委员列席区政协常委扩大会议, 子节点分数=0, 累计分数=0
11:45:55.555 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加专委会组织的其他会议（非全体会议）, 策略键: attendance:committee:other, 是否有子节点: false
11:45:55.555 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: attendance:committee:other
11:45:55.555 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=参加专委会组织的其他会议（非全体会议）, 子节点分数=0, 累计分数=0
11:45:55.556 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加全国政协、省政协、市政协组织的相关会议, 策略键: attendance:higher:meeting, 是否有子节点: false
11:45:55.556 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: attendance:higher:meeting
11:45:55.556 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=参加全国政协、省政协、市政协组织的相关会议, 子节点分数=0, 累计分数=0
11:45:55.556 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=参加会议（以半天计）, 总分=0
11:45:55.556 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=基础分, 子节点=参加会议（以半天计）, 子节点分数=0, 累计分数=0
11:45:55.556 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加活动（以每次计，不间断活动记为1次）, 策略键: null, 是否有子节点: true
11:45:55.556 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加全国政协、省政协、市政协、区政协组织相关活动, 策略键: attendance:multi:activity, 是否有子节点: false
11:45:55.557 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: attendance:multi:activity
11:45:55.557 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加活动（以每次计，不间断活动记为1次）, 子节点=参加全国政协、省政协、市政协、区政协组织相关活动, 子节点分数=0, 累计分数=0
11:45:55.557 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 受区政协委托参加区委、区政府组成部门等单位组织的活动, 策略键: attendance:delegated:external, 是否有子节点: false
11:45:55.557 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: attendance:delegated:external
11:45:55.557 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加活动（以每次计，不间断活动记为1次）, 子节点=受区政协委托参加区委、区政府组成部门等单位组织的活动, 子节点分数=0, 累计分数=0
11:45:55.557 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加区政协组织的委员培训活动, 策略键: training:district:regular, 是否有子节点: false
11:45:55.557 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: training:district:regular
11:45:55.558 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加活动（以每次计，不间断活动记为1次）, 子节点=参加区政协组织的委员培训活动, 子节点分数=0, 累计分数=0
11:45:55.558 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加与区政协工作相关的各类会议与活动情况, 策略键: participation:related:all, 是否有子节点: false
11:45:55.558 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: participation:related:all
11:45:55.558 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加活动（以每次计，不间断活动记为1次）, 子节点=参加与区政协工作相关的各类会议与活动情况, 子节点分数=0, 累计分数=0
11:45:55.558 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=参加活动（以每次计，不间断活动记为1次）, 总分=0
11:45:55.558 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=基础分, 子节点=参加活动（以每次计，不间断活动记为1次）, 子节点分数=0, 累计分数=0
11:45:55.558 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 会议发言（以每件计或参加会议次数记）, 策略键: null, 是否有子节点: true
11:45:55.559 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 在全会、常委会、联组会议等重要会议上的发言（提交书面并口头发言）, 策略键: speech:plenary:both, 是否有子节点: false
11:45:55.559 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: speech:plenary:both
11:45:55.559 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=会议发言（以每件计或参加会议次数记）, 子节点=在全会、常委会、联组会议等重要会议上的发言（提交书面并口头发言）, 子节点分数=0, 累计分数=0
11:45:55.559 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 在全会、常委会、联组会议等重要会议上的发言（书面发言）, 策略键: speech:plenary:written, 是否有子节点: false
11:45:55.559 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: speech:plenary:written
11:45:55.559 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=会议发言（以每件计或参加会议次数记）, 子节点=在全会、常委会、联组会议等重要会议上的发言（书面发言）, 子节点分数=0, 累计分数=0
11:45:55.559 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 在专题协商会，对口协商、提案办理协商座谈会等其他会议上的发言（以参加会议次数记）, 策略键: speech:other:counted, 是否有子节点: false
11:45:55.560 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: speech:other:counted
11:45:55.560 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=会议发言（以每件计或参加会议次数记）, 子节点=在专题协商会，对口协商、提案办理协商座谈会等其他会议上的发言（以参加会议次数记）, 子节点分数=0, 累计分数=0
11:45:55.560 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=会议发言（以每件计或参加会议次数记）, 总分=0
11:45:55.560 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=基础分, 子节点=会议发言（以每件计或参加会议次数记）, 子节点分数=0, 累计分数=0
11:45:55.560 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 提交提案（以每件记，该提案分值取奖励分最高值）, 策略键: null, 是否有子节点: true
11:45:55.560 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 1年内不提交提案（按年度累计计分）, 策略键: proposal:penalty:annual, 是否有子节点: false
11:45:55.561 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=黄旭, 规则=1年内不提交提案（按年度累计计分）, 策略键=proposal:penalty:annual, 策略类型=BASIC, 方法=hasNotSubmittedProposalWithinOneYear
11:45:55.561 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=黄旭, 规则=1年内不提交提案（按年度累计计分）, 策略类型=BASIC, 方法名=hasNotSubmittedProposalWithinOneYear
11:45:55.561 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: hasNotSubmittedProposalWithinOneYear
11:45:55.562 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: hasNotSubmittedProposalWithinOneYear
11:45:55.562 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.hasNotSubmittedProposalWithinOneYear - [debug,135] - ==>  Preparing: SELECT EXISTS ( SELECT 1 FROM proposal p INNER JOIN proposal_user_rel pur ON p.id = pur.proposal_id WHERE p.del_flag = false AND pur.del_flag = false AND pur.proposer_id = ? AND p.year = ? )
11:45:55.562 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.hasNotSubmittedProposalWithinOneYear - [debug,135] - ==> Parameters: 255(String), 2024(String)
11:45:55.565 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.hasNotSubmittedProposalWithinOneYear - [debug,135] - <==      Total: 1
11:45:55.566 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=黄旭, 方法=hasNotSubmittedProposalWithinOneYear, 分数=0
11:45:55.566 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=黄旭, 规则=1年内不提交提案（按年度累计计分）, 分数=0
11:45:55.566 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=1年内不提交提案（按年度累计计分）, 子节点分数=0, 累计分数=0
11:45:55.566 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获立案的个人提案（第一提案人）, 策略键: proposal:accepted:primary, 是否有子节点: false
11:45:55.566 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=黄旭, 规则=获立案的个人提案（第一提案人）, 策略键=proposal:accepted:primary, 策略类型=BASIC, 方法=countApprovedProposalsAsPrimaryProposer
11:45:55.567 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=黄旭, 规则=获立案的个人提案（第一提案人）, 策略类型=BASIC, 方法名=countApprovedProposalsAsPrimaryProposer
11:45:55.567 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: countApprovedProposalsAsPrimaryProposer
11:45:55.567 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: countApprovedProposalsAsPrimaryProposer
11:45:55.567 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countApprovedProposalsAsPrimaryProposer - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM proposal p INNER JOIN proposal_user_rel pur ON p.id = pur.proposal_id AND pur.del_flag = false AND pur.proposer_id = ? AND pur.submit_type = '领衔' WHERE p.del_flag = false AND p.submit_type = 'INDIVIDUAL' AND p.year = ? AND p.case_filing IN ('PUT_ON', 'MERGED', 'WAIT_HANDLE', 'FINISH')
11:45:55.568 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countApprovedProposalsAsPrimaryProposer - [debug,135] - ==> Parameters: 255(String), 2024(String)
11:45:55.570 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countApprovedProposalsAsPrimaryProposer - [debug,135] - <==      Total: 1
11:45:55.571 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=黄旭, 方法=countApprovedProposalsAsPrimaryProposer, 分数=0
11:45:55.571 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=黄旭, 规则=获立案的个人提案（第一提案人）, 分数=0
11:45:55.571 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=获立案的个人提案（第一提案人）, 子节点分数=0, 累计分数=0
11:45:55.571 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获立案的个人提案（附议人）, 策略键: proposal:accepted:seconder, 是否有子节点: false
11:45:55.571 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=黄旭, 规则=获立案的个人提案（附议人）, 策略键=proposal:accepted:seconder, 策略类型=BASIC, 方法=countApprovedProposalsAsSecondaryProposer
11:45:55.572 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=黄旭, 规则=获立案的个人提案（附议人）, 策略类型=BASIC, 方法名=countApprovedProposalsAsSecondaryProposer
11:45:55.572 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: countApprovedProposalsAsSecondaryProposer
11:45:55.572 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: countApprovedProposalsAsSecondaryProposer
11:45:55.572 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countApprovedProposalsAsSecondaryProposer - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM proposal p INNER JOIN proposal_user_rel pur ON p.id = pur.proposal_id AND pur.del_flag = false AND pur.proposer_id = '附议' WHERE p.del_flag = false AND p.submit_type = 'INDIVIDUAL' AND p.year = ? AND p.case_filing IN ('PUT_ON', 'MERGED', 'WAIT_HANDLE', 'FINISH')
11:45:55.573 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countApprovedProposalsAsSecondaryProposer - [debug,135] - ==> Parameters: 2024(String)
11:45:55.574 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countApprovedProposalsAsSecondaryProposer - [debug,135] - <==      Total: 1
11:45:55.575 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=黄旭, 方法=countApprovedProposalsAsSecondaryProposer, 分数=0
11:45:55.575 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=黄旭, 规则=获立案的个人提案（附议人）, 分数=0
11:45:55.575 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=获立案的个人提案（附议人）, 子节点分数=0, 累计分数=0
11:45:55.575 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获重点提案（第一提案人）, 策略键: proposal:key:primary, 是否有子节点: false
11:45:55.575 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: proposal:key:primary
11:45:55.575 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=获重点提案（第一提案人）, 子节点分数=0, 累计分数=0
11:45:55.575 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获重点提案（附议人）, 策略键: proposal:key:seconder, 是否有子节点: false
11:45:55.575 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: proposal:key:seconder
11:45:55.577 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=获重点提案（附议人）, 子节点分数=0, 累计分数=0
11:45:55.577 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获区政协主席会议成员和区政府领导督办的提案（第一提案人）, 策略键: proposal:supervised:primary, 是否有子节点: false
11:45:55.577 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: proposal:supervised:primary
11:45:55.577 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=获区政协主席会议成员和区政府领导督办的提案（第一提案人）, 子节点分数=0, 累计分数=0
11:45:55.577 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获区政协主席会议成员和区政府领导督办的提案（附议人）, 策略键: proposal:supervised:seconder, 是否有子节点: false
11:45:55.577 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: proposal:supervised:seconder
11:45:55.577 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=获区政协主席会议成员和区政府领导督办的提案（附议人）, 子节点分数=0, 累计分数=0
11:45:55.578 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=提交提案（以每件记，该提案分值取奖励分最高值）, 总分=0
11:45:55.578 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=基础分, 子节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点分数=0, 累计分数=0
11:45:55.578 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 社情民意（以每篇计，该篇分值取奖励分最高值）, 策略键: null, 是否有子节点: true
11:45:55.578 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 1年内不提交社情民意（按年度累计计分）, 策略键: opinion:penalty:annual, 是否有子节点: false
11:45:55.578 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=黄旭, 规则=1年内不提交社情民意（按年度累计计分）, 策略键=opinion:penalty:annual, 策略类型=BASIC, 方法=hasNotSubmittedManuscriptWithinOneYear
11:45:55.578 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=黄旭, 规则=1年内不提交社情民意（按年度累计计分）, 策略类型=BASIC, 方法名=hasNotSubmittedManuscriptWithinOneYear
11:45:55.578 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: hasNotSubmittedManuscriptWithinOneYear
11:45:55.579 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: hasNotSubmittedManuscriptWithinOneYear
11:45:55.579 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.hasNotSubmittedManuscriptWithinOneYear - [debug,135] - ==>  Preparing: SELECT EXISTS ( SELECT 1 FROM proposal p INNER JOIN proposal_user_rel pur ON p.id = pur.proposal_id WHERE p.del_flag = false AND pur.del_flag = false AND pur.proposer_id = ? AND p.year = ? )
11:45:55.580 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.hasNotSubmittedManuscriptWithinOneYear - [debug,135] - ==> Parameters: 255(String), 2024(String)
11:45:55.581 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.hasNotSubmittedManuscriptWithinOneYear - [debug,135] - <==      Total: 1
11:45:55.582 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=黄旭, 方法=hasNotSubmittedManuscriptWithinOneYear, 分数=0
11:45:55.582 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=黄旭, 规则=1年内不提交社情民意（按年度累计计分）, 分数=0
11:45:55.582 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点=1年内不提交社情民意（按年度累计计分）, 子节点分数=0, 累计分数=0
11:45:55.582 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 社情民意被采纳, 策略键: opinion:accepted:basic, 是否有子节点: false
11:45:55.583 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=黄旭, 规则=社情民意被采纳, 策略键=opinion:accepted:basic, 策略类型=BASIC, 方法=countAcceptedManuscripts
11:45:55.583 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=黄旭, 规则=社情民意被采纳, 策略类型=BASIC, 方法名=countAcceptedManuscripts
11:45:55.583 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: countAcceptedManuscripts
11:45:55.583 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: countAcceptedManuscripts
11:45:55.584 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countAcceptedManuscripts - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM manuscript m INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false WHERE m.del_flag = false AND m.status != 'DISCARD' AND m.adopt_way != 'NOT_ADOPT' AND m.year = ? AND mrl.user_id = ?
11:45:55.585 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countAcceptedManuscripts - [debug,135] - ==> Parameters: 2024(String), 255(String)
11:45:55.586 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countAcceptedManuscripts - [debug,135] - <==      Total: 1
11:45:55.587 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=黄旭, 方法=countAcceptedManuscripts, 分数=0
11:45:55.587 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=黄旭, 规则=社情民意被采纳, 分数=0
11:45:55.587 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点=社情民意被采纳, 子节点分数=0, 累计分数=0
11:45:55.587 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 得到区领导批示, 策略键: opinion:endorsed:district, 是否有子节点: false
11:45:55.588 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=黄旭, 规则=得到区领导批示, 策略键=opinion:endorsed:district, 策略类型=BASIC, 方法=countDistrictEndorsedManuscript
11:45:55.588 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=黄旭, 规则=得到区领导批示, 策略类型=BASIC, 方法名=countDistrictEndorsedManuscript
11:45:55.588 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: countDistrictEndorsedManuscript
11:45:55.588 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: countDistrictEndorsedManuscript
11:45:55.588 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countDistrictEndorsedManuscript - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM manuscript m INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false INNER JOIN manuscript_endorsement me ON m.id = me.manuscript_id AND me.del_flag = false WHERE m.del_flag = false AND m.year = ? AND mrl.user_id = ? AND FIND_IN_SET('DISTRICT', me.endorse_type) > 0
11:45:55.590 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countDistrictEndorsedManuscript - [debug,135] - ==> Parameters: 2024(String), 255(String)
11:45:55.592 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countDistrictEndorsedManuscript - [debug,135] - <==      Total: 1
11:45:55.592 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=黄旭, 方法=countDistrictEndorsedManuscript, 分数=0
11:45:55.592 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=黄旭, 规则=得到区领导批示, 分数=0
11:45:55.593 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点=得到区领导批示, 子节点分数=0, 累计分数=0
11:45:55.593 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 得到市领导批示, 策略键: opinion:endorsed:municipal, 是否有子节点: false
11:45:55.593 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=黄旭, 规则=得到市领导批示, 策略键=opinion:endorsed:municipal, 策略类型=BASIC, 方法=countCityEndorsedManuscript
11:45:55.593 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=黄旭, 规则=得到市领导批示, 策略类型=BASIC, 方法名=countCityEndorsedManuscript
11:45:55.593 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: countCityEndorsedManuscript
11:45:55.593 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: countCityEndorsedManuscript
11:45:55.594 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countCityEndorsedManuscript - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM manuscript m INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false INNER JOIN manuscript_endorsement me ON m.id = me.manuscript_id AND me.del_flag = false WHERE m.del_flag = false AND m.year = ? AND mrl.user_id = ? AND FIND_IN_SET('CITY', me.endorse_type) > 0
11:45:55.595 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countCityEndorsedManuscript - [debug,135] - ==> Parameters: 2024(String), 255(String)
11:45:55.597 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countCityEndorsedManuscript - [debug,135] - <==      Total: 1
11:45:55.599 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=黄旭, 方法=countCityEndorsedManuscript, 分数=0
11:45:55.599 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=黄旭, 规则=得到市领导批示, 分数=0
11:45:55.599 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点=得到市领导批示, 子节点分数=0, 累计分数=0
11:45:55.599 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 被省政协采用, 策略键: opinion:adopted:province, 是否有子节点: false
11:45:55.599 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: opinion:adopted:province
11:45:55.600 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点=被省政协采用, 子节点分数=0, 累计分数=0
11:45:55.600 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 得到省领导批示, 策略键: opinion:endorsed:provincial, 是否有子节点: false
11:45:55.600 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=黄旭, 规则=得到省领导批示, 策略键=opinion:endorsed:provincial, 策略类型=BASIC, 方法=countProvinceEndorsedManuscript
11:45:55.600 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=黄旭, 规则=得到省领导批示, 策略类型=BASIC, 方法名=countProvinceEndorsedManuscript
11:45:55.600 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: countProvinceEndorsedManuscript
11:45:55.600 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: countProvinceEndorsedManuscript
11:45:55.601 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countProvinceEndorsedManuscript - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM manuscript m INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false INNER JOIN manuscript_endorsement me ON m.id = me.manuscript_id AND me.del_flag = false WHERE m.del_flag = false AND m.year = ? AND mrl.user_id = ? AND FIND_IN_SET('PROVINCE', me.endorse_type) > 0
11:45:55.603 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countProvinceEndorsedManuscript - [debug,135] - ==> Parameters: 2024(String), 255(String)
11:45:55.605 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countProvinceEndorsedManuscript - [debug,135] - <==      Total: 1
11:45:55.607 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=黄旭, 方法=countProvinceEndorsedManuscript, 分数=0
11:45:55.607 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=黄旭, 规则=得到省领导批示, 分数=0
11:45:55.607 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点=得到省领导批示, 子节点分数=0, 累计分数=0
11:45:55.607 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 总分=0
11:45:55.608 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=基础分, 子节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点分数=0, 累计分数=0
11:45:55.608 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=基础分, 总分=0
11:45:55.608 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,482] - 顶级规则 基础分 最终分数: 0
11:45:55.608 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,487] - 累加基础分: 0
11:45:55.608 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,476] - 处理顶级规则: 奖励分
11:45:55.608 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 奖励分, 策略键: null, 是否有子节点: true
11:45:55.608 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 撰写调研报告或其它文字资料（以每篇计）, 策略键: null, 是否有子节点: true
11:45:55.608 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 调研报告被采用（执笔者）, 策略键: research_report:adopted:writer, 是否有子节点: false
11:45:55.608 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: research_report:adopted:writer
11:45:55.608 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=调研报告被采用（执笔者）, 子节点分数=0, 累计分数=0
11:45:55.608 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 调研报告被采用（参与者）, 策略键: research_report:adopted:participant, 是否有子节点: false
11:45:55.608 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: research_report:adopted:participant
11:45:55.608 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=调研报告被采用（参与者）, 子节点分数=0, 累计分数=0
11:45:55.610 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 调研报告被奖励（执笔者）, 策略键: research_report:rewarded:writer, 是否有子节点: false
11:45:55.610 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: research_report:rewarded:writer
11:45:55.610 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=调研报告被奖励（执笔者）, 子节点分数=0, 累计分数=0
11:45:55.610 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 调研报告被奖励（参与者）, 策略键: research_report:rewarded:participant, 是否有子节点: false
11:45:55.610 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: research_report:rewarded:participant
11:45:55.610 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=调研报告被奖励（参与者）, 子节点分数=0, 累计分数=0
11:45:55.610 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 调研报告被区领导批示（执笔者）, 策略键: research_report:endorsed_district:writer, 是否有子节点: false
11:45:55.610 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: research_report:endorsed_district:writer
11:45:55.611 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=调研报告被区领导批示（执笔者）, 子节点分数=0, 累计分数=0
11:45:55.611 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 调研报告被区领导批示（参与者）, 策略键: research_report:endorsed_district:participant, 是否有子节点: false
11:45:55.611 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: research_report:endorsed_district:participant
11:45:55.611 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=调研报告被区领导批示（参与者）, 子节点分数=0, 累计分数=0
11:45:55.611 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 发表文章（被采纳、刊登、刊用）, 策略键: article:published, 是否有子节点: false
11:45:55.611 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: article:published
11:45:55.611 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=发表文章（被采纳、刊登、刊用）, 子节点分数=0, 累计分数=0
11:45:55.611 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 发表文章（获奖）, 策略键: article:awarded, 是否有子节点: false
11:45:55.611 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: article:awarded
11:45:55.612 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=发表文章（获奖）, 子节点分数=0, 累计分数=0
11:45:55.612 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=撰写调研报告或其它文字资料（以每篇计）, 总分=0
11:45:55.612 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=奖励分, 子节点=撰写调研报告或其它文字资料（以每篇计）, 子节点分数=0, 累计分数=0
11:45:55.612 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加区政协年度重点工作, 策略键: null, 是否有子节点: true
11:45:55.612 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动（特别突出）, 策略键: env_protection:supervision:outstanding, 是否有子节点: false
11:45:55.612 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: env_protection:supervision:outstanding
11:45:55.612 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.612 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动（表现较好）, 策略键: env_protection:supervision:good, 是否有子节点: false
11:45:55.612 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: env_protection:supervision:good
11:45:55.613 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.613 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动（有所参与）, 策略键: env_protection:supervision:participated, 是否有子节点: false
11:45:55.613 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: env_protection:supervision:participated
11:45:55.613 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.613 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加脱贫攻坚与乡村振兴工作（特别突出）, 策略键: rural_revitalization:outstanding, 是否有子节点: false
11:45:55.613 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: rural_revitalization:outstanding
11:45:55.613 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加脱贫攻坚与乡村振兴工作（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.613 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加脱贫攻坚与乡村振兴工作（表现较好）, 策略键: rural_revitalization:good, 是否有子节点: false
11:45:55.614 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: rural_revitalization:good
11:45:55.614 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加脱贫攻坚与乡村振兴工作（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.614 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加脱贫攻坚与乡村振兴工作（有所参与）, 策略键: rural_revitalization:participated, 是否有子节点: false
11:45:55.614 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: rural_revitalization:participated
11:45:55.614 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加脱贫攻坚与乡村振兴工作（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.614 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（特别突出）, 策略键: grassroots_democracy:outstanding, 是否有子节点: false
11:45:55.614 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: grassroots_democracy:outstanding
11:45:55.615 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.615 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（表现较好）, 策略键: grassroots_democracy:good, 是否有子节点: false
11:45:55.615 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: grassroots_democracy:good
11:45:55.615 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.615 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（有所参与）, 策略键: grassroots_democracy:participated, 是否有子节点: false
11:45:55.615 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: grassroots_democracy:participated
11:45:55.615 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.615 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加书香政协学习、宣讲活动（特别突出）, 策略键: reading_activities:outstanding, 是否有子节点: false
11:45:55.616 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: reading_activities:outstanding
11:45:55.616 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加书香政协学习、宣讲活动（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.616 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加书香政协学习、宣讲活动（表现较好）, 策略键: reading_activities:good, 是否有子节点: false
11:45:55.616 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: reading_activities:good
11:45:55.616 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加书香政协学习、宣讲活动（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.616 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加书香政协学习、宣讲活动（有所参与）, 策略键: reading_activities:participated, 是否有子节点: false
11:45:55.616 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: reading_activities:participated
11:45:55.617 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加书香政协学习、宣讲活动（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.617 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加新冠肺炎疫情防控（特别突出）, 策略键: epidemic_prevention:outstanding, 是否有子节点: false
11:45:55.617 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: epidemic_prevention:outstanding
11:45:55.617 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加新冠肺炎疫情防控（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.617 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加新冠肺炎疫情防控（表现较好）, 策略键: epidemic_prevention:good, 是否有子节点: false
11:45:55.617 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: epidemic_prevention:good
11:45:55.617 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加新冠肺炎疫情防控（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.618 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加新冠肺炎疫情防控（有所参与）, 策略键: epidemic_prevention:participated, 是否有子节点: false
11:45:55.618 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: epidemic_prevention:participated
11:45:55.618 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加新冠肺炎疫情防控（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.618 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 助力项目建设服务，参加引进外资活动（特别突出）, 策略键: project_service:outstanding, 是否有子节点: false
11:45:55.618 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: project_service:outstanding
11:45:55.619 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=助力项目建设服务，参加引进外资活动（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.619 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 助力项目建设服务，参加引进外资活动（表现较好）, 策略键: project_service:good, 是否有子节点: false
11:45:55.619 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: project_service:good
11:45:55.619 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=助力项目建设服务，参加引进外资活动（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.619 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 助力项目建设服务，参加引进外资活动（有所参与）, 策略键: project_service:participated, 是否有子节点: false
11:45:55.619 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: project_service:participated
11:45:55.619 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=助力项目建设服务，参加引进外资活动（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.619 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 助推创一流营商环境（特别突出）, 策略键: business_environment:outstanding, 是否有子节点: false
11:45:55.620 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: business_environment:outstanding
11:45:55.620 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=助推创一流营商环境（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.620 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 助推创一流营商环境（表现较好）, 策略键: business_environment:good, 是否有子节点: false
11:45:55.620 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: business_environment:good
11:45:55.620 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=助推创一流营商环境（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.620 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 助推创一流营商环境（有所参与）, 策略键: business_environment:participated, 是否有子节点: false
11:45:55.620 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: business_environment:participated
11:45:55.620 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=助推创一流营商环境（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.620 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 完成区政协交办的其他任务（特别突出）, 策略键: other_tasks:outstanding, 是否有子节点: false
11:45:55.620 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: other_tasks:outstanding
11:45:55.620 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=完成区政协交办的其他任务（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.620 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 完成区政协交办的其他任务（表现较好）, 策略键: other_tasks:good, 是否有子节点: false
11:45:55.621 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: other_tasks:good
11:45:55.621 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=完成区政协交办的其他任务（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.621 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 完成区政协交办的其他任务（有所参与）, 策略键: other_tasks:participated, 是否有子节点: false
11:45:55.621 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: other_tasks:participated
11:45:55.621 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=完成区政协交办的其他任务（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.621 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=参加区政协年度重点工作, 总分=0
11:45:55.621 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=奖励分, 子节点=参加区政协年度重点工作, 子节点分数=0, 累计分数=0
11:45:55.621 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获得奖励（如优秀政协委员、道德模范等，以次数计）, 策略键: null, 是否有子节点: true
11:45:55.622 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获得奖励（区级）, 策略键: awards:district, 是否有子节点: false
11:45:55.622 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: awards:district
11:45:55.622 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=获得奖励（如优秀政协委员、道德模范等，以次数计）, 子节点=获得奖励（区级）, 子节点分数=0, 累计分数=0
11:45:55.622 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获得奖励（市级）, 策略键: awards:municipal, 是否有子节点: false
11:45:55.622 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: awards:municipal
11:45:55.622 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=获得奖励（如优秀政协委员、道德模范等，以次数计）, 子节点=获得奖励（市级）, 子节点分数=0, 累计分数=0
11:45:55.622 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获得奖励（省级）, 策略键: awards:provincial, 是否有子节点: false
11:45:55.622 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: awards:provincial
11:45:55.622 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=获得奖励（如优秀政协委员、道德模范等，以次数计）, 子节点=获得奖励（省级）, 子节点分数=0, 累计分数=0
11:45:55.623 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获得奖励（国家级）, 策略键: awards:national, 是否有子节点: false
11:45:55.623 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: awards:national
11:45:55.623 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=获得奖励（如优秀政协委员、道德模范等，以次数计）, 子节点=获得奖励（国家级）, 子节点分数=0, 累计分数=0
11:45:55.623 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=获得奖励（如优秀政协委员、道德模范等，以次数计）, 总分=0
11:45:55.623 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=奖励分, 子节点=获得奖励（如优秀政协委员、道德模范等，以次数计）, 子节点分数=0, 累计分数=0
11:45:55.623 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 办好事、解难题、做公益情况, 策略键: null, 是否有子节点: true
11:45:55.623 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 以政协委员身份为人民群众办好事、解难题、做公益慈善等贡献突出的1次, 策略键: public_welfare:outstanding, 是否有子节点: false
11:45:55.623 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: public_welfare:outstanding
11:45:55.623 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=办好事、解难题、做公益情况, 子节点=以政协委员身份为人民群众办好事、解难题、做公益慈善等贡献突出的1次, 子节点分数=0, 累计分数=0
11:45:55.623 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=办好事、解难题、做公益情况, 总分=0
11:45:55.623 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=奖励分, 子节点=办好事、解难题、做公益情况, 子节点分数=0, 累计分数=0
11:45:55.624 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=奖励分, 总分=0
11:45:55.624 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,482] - 顶级规则 奖励分 最终分数: 0
11:45:55.624 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,491] - 累加奖励分: 0
11:45:55.624 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,495] - 委员 黄旭 计算完成: 基础分=0, 奖励分=0, 总分=0
11:45:55.624 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,470] - 开始计算委员: 李晓松
11:45:55.624 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,476] - 处理顶级规则: 基础分
11:45:55.624 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 基础分, 策略键: null, 是否有子节点: true
11:45:55.624 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加会议（以半天计）, 策略键: null, 是否有子节点: true
11:45:55.625 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 缺席区政协全体会议1次（未请假）, 策略键: absence:plenary:unexcused, 是否有子节点: false
11:45:55.625 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: absence:plenary:unexcused
11:45:55.625 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=缺席区政协全体会议1次（未请假）, 子节点分数=0, 累计分数=0
11:45:55.625 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 缺席区政协全体会议1次（请假）, 策略键: absence:plenary:excused, 是否有子节点: false
11:45:55.626 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: absence:plenary:excused
11:45:55.626 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=缺席区政协全体会议1次（请假）, 子节点分数=0, 累计分数=0
11:45:55.626 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 缺席区政协常委会议1次（适用于区政协常委）（未请假）, 策略键: absence:standing:unexcused, 是否有子节点: false
11:45:55.626 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: absence:standing:unexcused
11:45:55.626 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=缺席区政协常委会议1次（适用于区政协常委）（未请假）, 子节点分数=0, 累计分数=0
11:45:55.626 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 缺席区政协常委会议1次（适用于区政协常委）（请假）, 策略键: absence:standing:excused, 是否有子节点: false
11:45:55.628 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: absence:standing:excused
11:45:55.628 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=缺席区政协常委会议1次（适用于区政协常委）（请假）, 子节点分数=0, 累计分数=0
11:45:55.628 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 缺席区政协专门委员会会议1次（未请假）, 策略键: absence:committee:unexcused, 是否有子节点: false
11:45:55.628 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: absence:committee:unexcused
11:45:55.628 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=缺席区政协专门委员会会议1次（未请假）, 子节点分数=0, 累计分数=0
11:45:55.628 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 缺席区政协专门委员会会议1次（请假）, 策略键: absence:committee:excused, 是否有子节点: false
11:45:55.628 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: absence:committee:excused
11:45:55.628 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=缺席区政协专门委员会会议1次（请假）, 子节点分数=0, 累计分数=0
11:45:55.628 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 区政协委员列席区政协常委扩大会议, 策略键: attendance:standing:extended, 是否有子节点: false
11:45:55.628 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: attendance:standing:extended
11:45:55.628 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=区政协委员列席区政协常委扩大会议, 子节点分数=0, 累计分数=0
11:45:55.628 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加专委会组织的其他会议（非全体会议）, 策略键: attendance:committee:other, 是否有子节点: false
11:45:55.628 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: attendance:committee:other
11:45:55.628 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=参加专委会组织的其他会议（非全体会议）, 子节点分数=0, 累计分数=0
11:45:55.628 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加全国政协、省政协、市政协组织的相关会议, 策略键: attendance:higher:meeting, 是否有子节点: false
11:45:55.628 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: attendance:higher:meeting
11:45:55.628 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=参加全国政协、省政协、市政协组织的相关会议, 子节点分数=0, 累计分数=0
11:45:55.628 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=参加会议（以半天计）, 总分=0
11:45:55.628 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=基础分, 子节点=参加会议（以半天计）, 子节点分数=0, 累计分数=0
11:45:55.628 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加活动（以每次计，不间断活动记为1次）, 策略键: null, 是否有子节点: true
11:45:55.628 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加全国政协、省政协、市政协、区政协组织相关活动, 策略键: attendance:multi:activity, 是否有子节点: false
11:45:55.628 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: attendance:multi:activity
11:45:55.628 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加活动（以每次计，不间断活动记为1次）, 子节点=参加全国政协、省政协、市政协、区政协组织相关活动, 子节点分数=0, 累计分数=0
11:45:55.628 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 受区政协委托参加区委、区政府组成部门等单位组织的活动, 策略键: attendance:delegated:external, 是否有子节点: false
11:45:55.630 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: attendance:delegated:external
11:45:55.630 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加活动（以每次计，不间断活动记为1次）, 子节点=受区政协委托参加区委、区政府组成部门等单位组织的活动, 子节点分数=0, 累计分数=0
11:45:55.630 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加区政协组织的委员培训活动, 策略键: training:district:regular, 是否有子节点: false
11:45:55.630 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: training:district:regular
11:45:55.630 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加活动（以每次计，不间断活动记为1次）, 子节点=参加区政协组织的委员培训活动, 子节点分数=0, 累计分数=0
11:45:55.630 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加与区政协工作相关的各类会议与活动情况, 策略键: participation:related:all, 是否有子节点: false
11:45:55.630 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: participation:related:all
11:45:55.631 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加活动（以每次计，不间断活动记为1次）, 子节点=参加与区政协工作相关的各类会议与活动情况, 子节点分数=0, 累计分数=0
11:45:55.631 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=参加活动（以每次计，不间断活动记为1次）, 总分=0
11:45:55.631 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=基础分, 子节点=参加活动（以每次计，不间断活动记为1次）, 子节点分数=0, 累计分数=0
11:45:55.631 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 会议发言（以每件计或参加会议次数记）, 策略键: null, 是否有子节点: true
11:45:55.631 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 在全会、常委会、联组会议等重要会议上的发言（提交书面并口头发言）, 策略键: speech:plenary:both, 是否有子节点: false
11:45:55.631 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: speech:plenary:both
11:45:55.631 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=会议发言（以每件计或参加会议次数记）, 子节点=在全会、常委会、联组会议等重要会议上的发言（提交书面并口头发言）, 子节点分数=0, 累计分数=0
11:45:55.631 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 在全会、常委会、联组会议等重要会议上的发言（书面发言）, 策略键: speech:plenary:written, 是否有子节点: false
11:45:55.631 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: speech:plenary:written
11:45:55.631 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=会议发言（以每件计或参加会议次数记）, 子节点=在全会、常委会、联组会议等重要会议上的发言（书面发言）, 子节点分数=0, 累计分数=0
11:45:55.632 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 在专题协商会，对口协商、提案办理协商座谈会等其他会议上的发言（以参加会议次数记）, 策略键: speech:other:counted, 是否有子节点: false
11:45:55.632 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: speech:other:counted
11:45:55.632 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=会议发言（以每件计或参加会议次数记）, 子节点=在专题协商会，对口协商、提案办理协商座谈会等其他会议上的发言（以参加会议次数记）, 子节点分数=0, 累计分数=0
11:45:55.632 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=会议发言（以每件计或参加会议次数记）, 总分=0
11:45:55.632 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=基础分, 子节点=会议发言（以每件计或参加会议次数记）, 子节点分数=0, 累计分数=0
11:45:55.632 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 提交提案（以每件记，该提案分值取奖励分最高值）, 策略键: null, 是否有子节点: true
11:45:55.632 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 1年内不提交提案（按年度累计计分）, 策略键: proposal:penalty:annual, 是否有子节点: false
11:45:55.632 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=李晓松, 规则=1年内不提交提案（按年度累计计分）, 策略键=proposal:penalty:annual, 策略类型=BASIC, 方法=hasNotSubmittedProposalWithinOneYear
11:45:55.632 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=李晓松, 规则=1年内不提交提案（按年度累计计分）, 策略类型=BASIC, 方法名=hasNotSubmittedProposalWithinOneYear
11:45:55.632 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: hasNotSubmittedProposalWithinOneYear
11:45:55.632 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: hasNotSubmittedProposalWithinOneYear
11:45:55.633 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.hasNotSubmittedProposalWithinOneYear - [debug,135] - ==>  Preparing: SELECT EXISTS ( SELECT 1 FROM proposal p INNER JOIN proposal_user_rel pur ON p.id = pur.proposal_id WHERE p.del_flag = false AND pur.del_flag = false AND pur.proposer_id = ? AND p.year = ? )
11:45:55.633 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.hasNotSubmittedProposalWithinOneYear - [debug,135] - ==> Parameters: 153(String), 2024(String)
11:45:55.634 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.hasNotSubmittedProposalWithinOneYear - [debug,135] - <==      Total: 1
11:45:55.635 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=李晓松, 方法=hasNotSubmittedProposalWithinOneYear, 分数=0
11:45:55.635 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=李晓松, 规则=1年内不提交提案（按年度累计计分）, 分数=0
11:45:55.635 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=1年内不提交提案（按年度累计计分）, 子节点分数=0, 累计分数=0
11:45:55.635 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获立案的个人提案（第一提案人）, 策略键: proposal:accepted:primary, 是否有子节点: false
11:45:55.635 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=李晓松, 规则=获立案的个人提案（第一提案人）, 策略键=proposal:accepted:primary, 策略类型=BASIC, 方法=countApprovedProposalsAsPrimaryProposer
11:45:55.635 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=李晓松, 规则=获立案的个人提案（第一提案人）, 策略类型=BASIC, 方法名=countApprovedProposalsAsPrimaryProposer
11:45:55.636 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: countApprovedProposalsAsPrimaryProposer
11:45:55.636 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: countApprovedProposalsAsPrimaryProposer
11:45:55.636 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countApprovedProposalsAsPrimaryProposer - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM proposal p INNER JOIN proposal_user_rel pur ON p.id = pur.proposal_id AND pur.del_flag = false AND pur.proposer_id = ? AND pur.submit_type = '领衔' WHERE p.del_flag = false AND p.submit_type = 'INDIVIDUAL' AND p.year = ? AND p.case_filing IN ('PUT_ON', 'MERGED', 'WAIT_HANDLE', 'FINISH')
11:45:55.636 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countApprovedProposalsAsPrimaryProposer - [debug,135] - ==> Parameters: 153(String), 2024(String)
11:45:55.637 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countApprovedProposalsAsPrimaryProposer - [debug,135] - <==      Total: 1
11:45:55.638 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=李晓松, 方法=countApprovedProposalsAsPrimaryProposer, 分数=0
11:45:55.638 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=李晓松, 规则=获立案的个人提案（第一提案人）, 分数=0
11:45:55.638 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=获立案的个人提案（第一提案人）, 子节点分数=0, 累计分数=0
11:45:55.638 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获立案的个人提案（附议人）, 策略键: proposal:accepted:seconder, 是否有子节点: false
11:45:55.638 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=李晓松, 规则=获立案的个人提案（附议人）, 策略键=proposal:accepted:seconder, 策略类型=BASIC, 方法=countApprovedProposalsAsSecondaryProposer
11:45:55.638 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=李晓松, 规则=获立案的个人提案（附议人）, 策略类型=BASIC, 方法名=countApprovedProposalsAsSecondaryProposer
11:45:55.638 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: countApprovedProposalsAsSecondaryProposer
11:45:55.638 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: countApprovedProposalsAsSecondaryProposer
11:45:55.639 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=李晓松, 方法=countApprovedProposalsAsSecondaryProposer, 分数=0
11:45:55.639 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=李晓松, 规则=获立案的个人提案（附议人）, 分数=0
11:45:55.639 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=获立案的个人提案（附议人）, 子节点分数=0, 累计分数=0
11:45:55.639 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获重点提案（第一提案人）, 策略键: proposal:key:primary, 是否有子节点: false
11:45:55.640 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: proposal:key:primary
11:45:55.640 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=获重点提案（第一提案人）, 子节点分数=0, 累计分数=0
11:45:55.640 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获重点提案（附议人）, 策略键: proposal:key:seconder, 是否有子节点: false
11:45:55.640 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: proposal:key:seconder
11:45:55.640 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=获重点提案（附议人）, 子节点分数=0, 累计分数=0
11:45:55.640 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获区政协主席会议成员和区政府领导督办的提案（第一提案人）, 策略键: proposal:supervised:primary, 是否有子节点: false
11:45:55.640 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: proposal:supervised:primary
11:45:55.640 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=获区政协主席会议成员和区政府领导督办的提案（第一提案人）, 子节点分数=0, 累计分数=0
11:45:55.640 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获区政协主席会议成员和区政府领导督办的提案（附议人）, 策略键: proposal:supervised:seconder, 是否有子节点: false
11:45:55.640 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: proposal:supervised:seconder
11:45:55.640 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=获区政协主席会议成员和区政府领导督办的提案（附议人）, 子节点分数=0, 累计分数=0
11:45:55.641 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=提交提案（以每件记，该提案分值取奖励分最高值）, 总分=0
11:45:55.641 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=基础分, 子节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点分数=0, 累计分数=0
11:45:55.641 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 社情民意（以每篇计，该篇分值取奖励分最高值）, 策略键: null, 是否有子节点: true
11:45:55.641 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 1年内不提交社情民意（按年度累计计分）, 策略键: opinion:penalty:annual, 是否有子节点: false
11:45:55.641 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=李晓松, 规则=1年内不提交社情民意（按年度累计计分）, 策略键=opinion:penalty:annual, 策略类型=BASIC, 方法=hasNotSubmittedManuscriptWithinOneYear
11:45:55.641 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=李晓松, 规则=1年内不提交社情民意（按年度累计计分）, 策略类型=BASIC, 方法名=hasNotSubmittedManuscriptWithinOneYear
11:45:55.641 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: hasNotSubmittedManuscriptWithinOneYear
11:45:55.641 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: hasNotSubmittedManuscriptWithinOneYear
11:45:55.641 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.hasNotSubmittedManuscriptWithinOneYear - [debug,135] - ==>  Preparing: SELECT EXISTS ( SELECT 1 FROM proposal p INNER JOIN proposal_user_rel pur ON p.id = pur.proposal_id WHERE p.del_flag = false AND pur.del_flag = false AND pur.proposer_id = ? AND p.year = ? )
11:45:55.642 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.hasNotSubmittedManuscriptWithinOneYear - [debug,135] - ==> Parameters: 153(String), 2024(String)
11:45:55.643 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.hasNotSubmittedManuscriptWithinOneYear - [debug,135] - <==      Total: 1
11:45:55.643 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=李晓松, 方法=hasNotSubmittedManuscriptWithinOneYear, 分数=0
11:45:55.643 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=李晓松, 规则=1年内不提交社情民意（按年度累计计分）, 分数=0
11:45:55.644 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点=1年内不提交社情民意（按年度累计计分）, 子节点分数=0, 累计分数=0
11:45:55.644 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 社情民意被采纳, 策略键: opinion:accepted:basic, 是否有子节点: false
11:45:55.644 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=李晓松, 规则=社情民意被采纳, 策略键=opinion:accepted:basic, 策略类型=BASIC, 方法=countAcceptedManuscripts
11:45:55.644 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=李晓松, 规则=社情民意被采纳, 策略类型=BASIC, 方法名=countAcceptedManuscripts
11:45:55.644 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: countAcceptedManuscripts
11:45:55.644 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: countAcceptedManuscripts
11:45:55.644 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countAcceptedManuscripts - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM manuscript m INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false WHERE m.del_flag = false AND m.status != 'DISCARD' AND m.adopt_way != 'NOT_ADOPT' AND m.year = ? AND mrl.user_id = ?
11:45:55.645 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countAcceptedManuscripts - [debug,135] - ==> Parameters: 2024(String), 153(String)
11:45:55.646 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countAcceptedManuscripts - [debug,135] - <==      Total: 1
11:45:55.646 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=李晓松, 方法=countAcceptedManuscripts, 分数=0
11:45:55.646 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=李晓松, 规则=社情民意被采纳, 分数=0
11:45:55.647 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点=社情民意被采纳, 子节点分数=0, 累计分数=0
11:45:55.647 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 得到区领导批示, 策略键: opinion:endorsed:district, 是否有子节点: false
11:45:55.647 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=李晓松, 规则=得到区领导批示, 策略键=opinion:endorsed:district, 策略类型=BASIC, 方法=countDistrictEndorsedManuscript
11:45:55.647 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=李晓松, 规则=得到区领导批示, 策略类型=BASIC, 方法名=countDistrictEndorsedManuscript
11:45:55.647 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: countDistrictEndorsedManuscript
11:45:55.647 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: countDistrictEndorsedManuscript
11:45:55.647 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countDistrictEndorsedManuscript - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM manuscript m INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false INNER JOIN manuscript_endorsement me ON m.id = me.manuscript_id AND me.del_flag = false WHERE m.del_flag = false AND m.year = ? AND mrl.user_id = ? AND FIND_IN_SET('DISTRICT', me.endorse_type) > 0
11:45:55.648 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countDistrictEndorsedManuscript - [debug,135] - ==> Parameters: 2024(String), 153(String)
11:45:55.649 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countDistrictEndorsedManuscript - [debug,135] - <==      Total: 1
11:45:55.649 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=李晓松, 方法=countDistrictEndorsedManuscript, 分数=0
11:45:55.649 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=李晓松, 规则=得到区领导批示, 分数=0
11:45:55.650 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点=得到区领导批示, 子节点分数=0, 累计分数=0
11:45:55.650 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 得到市领导批示, 策略键: opinion:endorsed:municipal, 是否有子节点: false
11:45:55.650 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=李晓松, 规则=得到市领导批示, 策略键=opinion:endorsed:municipal, 策略类型=BASIC, 方法=countCityEndorsedManuscript
11:45:55.650 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=李晓松, 规则=得到市领导批示, 策略类型=BASIC, 方法名=countCityEndorsedManuscript
11:45:55.650 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: countCityEndorsedManuscript
11:45:55.650 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: countCityEndorsedManuscript
11:45:55.651 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countCityEndorsedManuscript - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM manuscript m INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false INNER JOIN manuscript_endorsement me ON m.id = me.manuscript_id AND me.del_flag = false WHERE m.del_flag = false AND m.year = ? AND mrl.user_id = ? AND FIND_IN_SET('CITY', me.endorse_type) > 0
11:45:55.651 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countCityEndorsedManuscript - [debug,135] - ==> Parameters: 2024(String), 153(String)
11:45:55.653 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countCityEndorsedManuscript - [debug,135] - <==      Total: 1
11:45:55.653 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=李晓松, 方法=countCityEndorsedManuscript, 分数=0
11:45:55.653 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=李晓松, 规则=得到市领导批示, 分数=0
11:45:55.653 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点=得到市领导批示, 子节点分数=0, 累计分数=0
11:45:55.653 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 被省政协采用, 策略键: opinion:adopted:province, 是否有子节点: false
11:45:55.653 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: opinion:adopted:province
11:45:55.654 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点=被省政协采用, 子节点分数=0, 累计分数=0
11:45:55.654 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 得到省领导批示, 策略键: opinion:endorsed:provincial, 是否有子节点: false
11:45:55.654 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=李晓松, 规则=得到省领导批示, 策略键=opinion:endorsed:provincial, 策略类型=BASIC, 方法=countProvinceEndorsedManuscript
11:45:55.654 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=李晓松, 规则=得到省领导批示, 策略类型=BASIC, 方法名=countProvinceEndorsedManuscript
11:45:55.654 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: countProvinceEndorsedManuscript
11:45:55.654 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: countProvinceEndorsedManuscript
11:45:55.654 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countProvinceEndorsedManuscript - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM manuscript m INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false INNER JOIN manuscript_endorsement me ON m.id = me.manuscript_id AND me.del_flag = false WHERE m.del_flag = false AND m.year = ? AND mrl.user_id = ? AND FIND_IN_SET('PROVINCE', me.endorse_type) > 0
11:45:55.655 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countProvinceEndorsedManuscript - [debug,135] - ==> Parameters: 2024(String), 153(String)
11:45:55.656 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countProvinceEndorsedManuscript - [debug,135] - <==      Total: 1
11:45:55.656 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=李晓松, 方法=countProvinceEndorsedManuscript, 分数=0
11:45:55.656 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=李晓松, 规则=得到省领导批示, 分数=0
11:45:55.656 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点=得到省领导批示, 子节点分数=0, 累计分数=0
11:45:55.656 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 总分=0
11:45:55.656 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=基础分, 子节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点分数=0, 累计分数=0
11:45:55.657 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=基础分, 总分=0
11:45:55.657 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,482] - 顶级规则 基础分 最终分数: 0
11:45:55.657 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,487] - 累加基础分: 0
11:45:55.657 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,476] - 处理顶级规则: 奖励分
11:45:55.657 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 奖励分, 策略键: null, 是否有子节点: true
11:45:55.657 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 撰写调研报告或其它文字资料（以每篇计）, 策略键: null, 是否有子节点: true
11:45:55.657 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 调研报告被采用（执笔者）, 策略键: research_report:adopted:writer, 是否有子节点: false
11:45:55.657 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: research_report:adopted:writer
11:45:55.657 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=调研报告被采用（执笔者）, 子节点分数=0, 累计分数=0
11:45:55.657 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 调研报告被采用（参与者）, 策略键: research_report:adopted:participant, 是否有子节点: false
11:45:55.657 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: research_report:adopted:participant
11:45:55.658 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=调研报告被采用（参与者）, 子节点分数=0, 累计分数=0
11:45:55.658 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 调研报告被奖励（执笔者）, 策略键: research_report:rewarded:writer, 是否有子节点: false
11:45:55.658 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: research_report:rewarded:writer
11:45:55.658 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=调研报告被奖励（执笔者）, 子节点分数=0, 累计分数=0
11:45:55.658 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 调研报告被奖励（参与者）, 策略键: research_report:rewarded:participant, 是否有子节点: false
11:45:55.658 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: research_report:rewarded:participant
11:45:55.658 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=调研报告被奖励（参与者）, 子节点分数=0, 累计分数=0
11:45:55.658 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 调研报告被区领导批示（执笔者）, 策略键: research_report:endorsed_district:writer, 是否有子节点: false
11:45:55.658 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: research_report:endorsed_district:writer
11:45:55.658 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=调研报告被区领导批示（执笔者）, 子节点分数=0, 累计分数=0
11:45:55.658 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 调研报告被区领导批示（参与者）, 策略键: research_report:endorsed_district:participant, 是否有子节点: false
11:45:55.658 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: research_report:endorsed_district:participant
11:45:55.659 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=调研报告被区领导批示（参与者）, 子节点分数=0, 累计分数=0
11:45:55.659 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 发表文章（被采纳、刊登、刊用）, 策略键: article:published, 是否有子节点: false
11:45:55.659 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: article:published
11:45:55.659 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=发表文章（被采纳、刊登、刊用）, 子节点分数=0, 累计分数=0
11:45:55.659 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 发表文章（获奖）, 策略键: article:awarded, 是否有子节点: false
11:45:55.659 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: article:awarded
11:45:55.659 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=发表文章（获奖）, 子节点分数=0, 累计分数=0
11:45:55.659 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=撰写调研报告或其它文字资料（以每篇计）, 总分=0
11:45:55.659 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=奖励分, 子节点=撰写调研报告或其它文字资料（以每篇计）, 子节点分数=0, 累计分数=0
11:45:55.659 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加区政协年度重点工作, 策略键: null, 是否有子节点: true
11:45:55.659 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动（特别突出）, 策略键: env_protection:supervision:outstanding, 是否有子节点: false
11:45:55.660 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: env_protection:supervision:outstanding
11:45:55.660 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.660 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动（表现较好）, 策略键: env_protection:supervision:good, 是否有子节点: false
11:45:55.660 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: env_protection:supervision:good
11:45:55.660 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.660 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动（有所参与）, 策略键: env_protection:supervision:participated, 是否有子节点: false
11:45:55.660 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: env_protection:supervision:participated
11:45:55.660 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.660 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加脱贫攻坚与乡村振兴工作（特别突出）, 策略键: rural_revitalization:outstanding, 是否有子节点: false
11:45:55.660 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: rural_revitalization:outstanding
11:45:55.660 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加脱贫攻坚与乡村振兴工作（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.661 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加脱贫攻坚与乡村振兴工作（表现较好）, 策略键: rural_revitalization:good, 是否有子节点: false
11:45:55.661 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: rural_revitalization:good
11:45:55.661 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加脱贫攻坚与乡村振兴工作（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.661 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加脱贫攻坚与乡村振兴工作（有所参与）, 策略键: rural_revitalization:participated, 是否有子节点: false
11:45:55.661 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: rural_revitalization:participated
11:45:55.661 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加脱贫攻坚与乡村振兴工作（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.661 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（特别突出）, 策略键: grassroots_democracy:outstanding, 是否有子节点: false
11:45:55.661 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: grassroots_democracy:outstanding
11:45:55.661 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.661 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（表现较好）, 策略键: grassroots_democracy:good, 是否有子节点: false
11:45:55.661 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: grassroots_democracy:good
11:45:55.661 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.662 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（有所参与）, 策略键: grassroots_democracy:participated, 是否有子节点: false
11:45:55.662 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: grassroots_democracy:participated
11:45:55.662 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.662 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加书香政协学习、宣讲活动（特别突出）, 策略键: reading_activities:outstanding, 是否有子节点: false
11:45:55.662 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: reading_activities:outstanding
11:45:55.662 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加书香政协学习、宣讲活动（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.662 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加书香政协学习、宣讲活动（表现较好）, 策略键: reading_activities:good, 是否有子节点: false
11:45:55.662 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: reading_activities:good
11:45:55.662 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加书香政协学习、宣讲活动（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.662 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加书香政协学习、宣讲活动（有所参与）, 策略键: reading_activities:participated, 是否有子节点: false
11:45:55.662 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: reading_activities:participated
11:45:55.662 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加书香政协学习、宣讲活动（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.663 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加新冠肺炎疫情防控（特别突出）, 策略键: epidemic_prevention:outstanding, 是否有子节点: false
11:45:55.663 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: epidemic_prevention:outstanding
11:45:55.663 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加新冠肺炎疫情防控（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.663 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加新冠肺炎疫情防控（表现较好）, 策略键: epidemic_prevention:good, 是否有子节点: false
11:45:55.663 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: epidemic_prevention:good
11:45:55.663 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加新冠肺炎疫情防控（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.663 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加新冠肺炎疫情防控（有所参与）, 策略键: epidemic_prevention:participated, 是否有子节点: false
11:45:55.663 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: epidemic_prevention:participated
11:45:55.663 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加新冠肺炎疫情防控（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.664 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 助力项目建设服务，参加引进外资活动（特别突出）, 策略键: project_service:outstanding, 是否有子节点: false
11:45:55.664 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: project_service:outstanding
11:45:55.664 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=助力项目建设服务，参加引进外资活动（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.664 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 助力项目建设服务，参加引进外资活动（表现较好）, 策略键: project_service:good, 是否有子节点: false
11:45:55.664 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: project_service:good
11:45:55.664 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=助力项目建设服务，参加引进外资活动（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.664 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 助力项目建设服务，参加引进外资活动（有所参与）, 策略键: project_service:participated, 是否有子节点: false
11:45:55.664 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: project_service:participated
11:45:55.664 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=助力项目建设服务，参加引进外资活动（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.664 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 助推创一流营商环境（特别突出）, 策略键: business_environment:outstanding, 是否有子节点: false
11:45:55.664 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: business_environment:outstanding
11:45:55.665 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=助推创一流营商环境（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.665 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 助推创一流营商环境（表现较好）, 策略键: business_environment:good, 是否有子节点: false
11:45:55.665 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: business_environment:good
11:45:55.665 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=助推创一流营商环境（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.665 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 助推创一流营商环境（有所参与）, 策略键: business_environment:participated, 是否有子节点: false
11:45:55.665 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: business_environment:participated
11:45:55.665 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=助推创一流营商环境（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.665 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 完成区政协交办的其他任务（特别突出）, 策略键: other_tasks:outstanding, 是否有子节点: false
11:45:55.665 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: other_tasks:outstanding
11:45:55.665 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=完成区政协交办的其他任务（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.665 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 完成区政协交办的其他任务（表现较好）, 策略键: other_tasks:good, 是否有子节点: false
11:45:55.665 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: other_tasks:good
11:45:55.666 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=完成区政协交办的其他任务（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.666 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 完成区政协交办的其他任务（有所参与）, 策略键: other_tasks:participated, 是否有子节点: false
11:45:55.666 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: other_tasks:participated
11:45:55.666 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=完成区政协交办的其他任务（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.666 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=参加区政协年度重点工作, 总分=0
11:45:55.666 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=奖励分, 子节点=参加区政协年度重点工作, 子节点分数=0, 累计分数=0
11:45:55.666 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获得奖励（如优秀政协委员、道德模范等，以次数计）, 策略键: null, 是否有子节点: true
11:45:55.666 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获得奖励（区级）, 策略键: awards:district, 是否有子节点: false
11:45:55.666 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: awards:district
11:45:55.666 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=获得奖励（如优秀政协委员、道德模范等，以次数计）, 子节点=获得奖励（区级）, 子节点分数=0, 累计分数=0
11:45:55.666 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获得奖励（市级）, 策略键: awards:municipal, 是否有子节点: false
11:45:55.666 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: awards:municipal
11:45:55.666 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=获得奖励（如优秀政协委员、道德模范等，以次数计）, 子节点=获得奖励（市级）, 子节点分数=0, 累计分数=0
11:45:55.667 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获得奖励（省级）, 策略键: awards:provincial, 是否有子节点: false
11:45:55.667 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: awards:provincial
11:45:55.667 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=获得奖励（如优秀政协委员、道德模范等，以次数计）, 子节点=获得奖励（省级）, 子节点分数=0, 累计分数=0
11:45:55.667 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获得奖励（国家级）, 策略键: awards:national, 是否有子节点: false
11:45:55.667 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: awards:national
11:45:55.667 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=获得奖励（如优秀政协委员、道德模范等，以次数计）, 子节点=获得奖励（国家级）, 子节点分数=0, 累计分数=0
11:45:55.667 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=获得奖励（如优秀政协委员、道德模范等，以次数计）, 总分=0
11:45:55.667 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=奖励分, 子节点=获得奖励（如优秀政协委员、道德模范等，以次数计）, 子节点分数=0, 累计分数=0
11:45:55.667 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 办好事、解难题、做公益情况, 策略键: null, 是否有子节点: true
11:45:55.667 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 以政协委员身份为人民群众办好事、解难题、做公益慈善等贡献突出的1次, 策略键: public_welfare:outstanding, 是否有子节点: false
11:45:55.668 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: public_welfare:outstanding
11:45:55.668 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=办好事、解难题、做公益情况, 子节点=以政协委员身份为人民群众办好事、解难题、做公益慈善等贡献突出的1次, 子节点分数=0, 累计分数=0
11:45:55.668 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=办好事、解难题、做公益情况, 总分=0
11:45:55.668 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=奖励分, 子节点=办好事、解难题、做公益情况, 子节点分数=0, 累计分数=0
11:45:55.668 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=奖励分, 总分=0
11:45:55.668 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,482] - 顶级规则 奖励分 最终分数: 0
11:45:55.668 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,491] - 累加奖励分: 0
11:45:55.668 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,495] - 委员 李晓松 计算完成: 基础分=0, 奖励分=0, 总分=0
11:45:55.668 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,470] - 开始计算委员: 刘玉
11:45:55.668 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,476] - 处理顶级规则: 基础分
11:45:55.668 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 基础分, 策略键: null, 是否有子节点: true
11:45:55.669 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加会议（以半天计）, 策略键: null, 是否有子节点: true
11:45:55.669 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 缺席区政协全体会议1次（未请假）, 策略键: absence:plenary:unexcused, 是否有子节点: false
11:45:55.669 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: absence:plenary:unexcused
11:45:55.669 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=缺席区政协全体会议1次（未请假）, 子节点分数=0, 累计分数=0
11:45:55.669 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 缺席区政协全体会议1次（请假）, 策略键: absence:plenary:excused, 是否有子节点: false
11:45:55.669 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: absence:plenary:excused
11:45:55.669 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=缺席区政协全体会议1次（请假）, 子节点分数=0, 累计分数=0
11:45:55.669 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 缺席区政协常委会议1次（适用于区政协常委）（未请假）, 策略键: absence:standing:unexcused, 是否有子节点: false
11:45:55.669 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: absence:standing:unexcused
11:45:55.669 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=缺席区政协常委会议1次（适用于区政协常委）（未请假）, 子节点分数=0, 累计分数=0
11:45:55.669 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 缺席区政协常委会议1次（适用于区政协常委）（请假）, 策略键: absence:standing:excused, 是否有子节点: false
11:45:55.669 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: absence:standing:excused
11:45:55.670 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=缺席区政协常委会议1次（适用于区政协常委）（请假）, 子节点分数=0, 累计分数=0
11:45:55.670 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 缺席区政协专门委员会会议1次（未请假）, 策略键: absence:committee:unexcused, 是否有子节点: false
11:45:55.670 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: absence:committee:unexcused
11:45:55.670 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=缺席区政协专门委员会会议1次（未请假）, 子节点分数=0, 累计分数=0
11:45:55.670 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 缺席区政协专门委员会会议1次（请假）, 策略键: absence:committee:excused, 是否有子节点: false
11:45:55.670 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: absence:committee:excused
11:45:55.670 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=缺席区政协专门委员会会议1次（请假）, 子节点分数=0, 累计分数=0
11:45:55.670 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 区政协委员列席区政协常委扩大会议, 策略键: attendance:standing:extended, 是否有子节点: false
11:45:55.670 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: attendance:standing:extended
11:45:55.670 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=区政协委员列席区政协常委扩大会议, 子节点分数=0, 累计分数=0
11:45:55.670 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加专委会组织的其他会议（非全体会议）, 策略键: attendance:committee:other, 是否有子节点: false
11:45:55.670 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: attendance:committee:other
11:45:55.670 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=参加专委会组织的其他会议（非全体会议）, 子节点分数=0, 累计分数=0
11:45:55.671 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加全国政协、省政协、市政协组织的相关会议, 策略键: attendance:higher:meeting, 是否有子节点: false
11:45:55.671 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: attendance:higher:meeting
11:45:55.671 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加会议（以半天计）, 子节点=参加全国政协、省政协、市政协组织的相关会议, 子节点分数=0, 累计分数=0
11:45:55.671 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=参加会议（以半天计）, 总分=0
11:45:55.671 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=基础分, 子节点=参加会议（以半天计）, 子节点分数=0, 累计分数=0
11:45:55.671 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加活动（以每次计，不间断活动记为1次）, 策略键: null, 是否有子节点: true
11:45:55.671 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加全国政协、省政协、市政协、区政协组织相关活动, 策略键: attendance:multi:activity, 是否有子节点: false
11:45:55.671 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: attendance:multi:activity
11:45:55.671 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加活动（以每次计，不间断活动记为1次）, 子节点=参加全国政协、省政协、市政协、区政协组织相关活动, 子节点分数=0, 累计分数=0
11:45:55.671 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 受区政协委托参加区委、区政府组成部门等单位组织的活动, 策略键: attendance:delegated:external, 是否有子节点: false
11:45:55.672 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: attendance:delegated:external
11:45:55.672 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加活动（以每次计，不间断活动记为1次）, 子节点=受区政协委托参加区委、区政府组成部门等单位组织的活动, 子节点分数=0, 累计分数=0
11:45:55.672 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加区政协组织的委员培训活动, 策略键: training:district:regular, 是否有子节点: false
11:45:55.672 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: training:district:regular
11:45:55.672 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加活动（以每次计，不间断活动记为1次）, 子节点=参加区政协组织的委员培训活动, 子节点分数=0, 累计分数=0
11:45:55.672 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加与区政协工作相关的各类会议与活动情况, 策略键: participation:related:all, 是否有子节点: false
11:45:55.672 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: participation:related:all
11:45:55.672 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加活动（以每次计，不间断活动记为1次）, 子节点=参加与区政协工作相关的各类会议与活动情况, 子节点分数=0, 累计分数=0
11:45:55.672 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=参加活动（以每次计，不间断活动记为1次）, 总分=0
11:45:55.672 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=基础分, 子节点=参加活动（以每次计，不间断活动记为1次）, 子节点分数=0, 累计分数=0
11:45:55.673 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 会议发言（以每件计或参加会议次数记）, 策略键: null, 是否有子节点: true
11:45:55.673 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 在全会、常委会、联组会议等重要会议上的发言（提交书面并口头发言）, 策略键: speech:plenary:both, 是否有子节点: false
11:45:55.673 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: speech:plenary:both
11:45:55.673 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=会议发言（以每件计或参加会议次数记）, 子节点=在全会、常委会、联组会议等重要会议上的发言（提交书面并口头发言）, 子节点分数=0, 累计分数=0
11:45:55.673 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 在全会、常委会、联组会议等重要会议上的发言（书面发言）, 策略键: speech:plenary:written, 是否有子节点: false
11:45:55.673 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: speech:plenary:written
11:45:55.673 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=会议发言（以每件计或参加会议次数记）, 子节点=在全会、常委会、联组会议等重要会议上的发言（书面发言）, 子节点分数=0, 累计分数=0
11:45:55.673 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 在专题协商会，对口协商、提案办理协商座谈会等其他会议上的发言（以参加会议次数记）, 策略键: speech:other:counted, 是否有子节点: false
11:45:55.673 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: speech:other:counted
11:45:55.673 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=会议发言（以每件计或参加会议次数记）, 子节点=在专题协商会，对口协商、提案办理协商座谈会等其他会议上的发言（以参加会议次数记）, 子节点分数=0, 累计分数=0
11:45:55.673 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=会议发言（以每件计或参加会议次数记）, 总分=0
11:45:55.673 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=基础分, 子节点=会议发言（以每件计或参加会议次数记）, 子节点分数=0, 累计分数=0
11:45:55.673 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 提交提案（以每件记，该提案分值取奖励分最高值）, 策略键: null, 是否有子节点: true
11:45:55.674 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 1年内不提交提案（按年度累计计分）, 策略键: proposal:penalty:annual, 是否有子节点: false
11:45:55.674 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=刘玉, 规则=1年内不提交提案（按年度累计计分）, 策略键=proposal:penalty:annual, 策略类型=BASIC, 方法=hasNotSubmittedProposalWithinOneYear
11:45:55.674 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=刘玉, 规则=1年内不提交提案（按年度累计计分）, 策略类型=BASIC, 方法名=hasNotSubmittedProposalWithinOneYear
11:45:55.674 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: hasNotSubmittedProposalWithinOneYear
11:45:55.674 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: hasNotSubmittedProposalWithinOneYear
11:45:55.674 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.hasNotSubmittedProposalWithinOneYear - [debug,135] - ==>  Preparing: SELECT EXISTS ( SELECT 1 FROM proposal p INNER JOIN proposal_user_rel pur ON p.id = pur.proposal_id WHERE p.del_flag = false AND pur.del_flag = false AND pur.proposer_id = ? AND p.year = ? )
11:45:55.675 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.hasNotSubmittedProposalWithinOneYear - [debug,135] - ==> Parameters: 203(String), 2024(String)
11:45:55.675 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.hasNotSubmittedProposalWithinOneYear - [debug,135] - <==      Total: 1
11:45:55.677 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=刘玉, 方法=hasNotSubmittedProposalWithinOneYear, 分数=0
11:45:55.677 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=刘玉, 规则=1年内不提交提案（按年度累计计分）, 分数=0
11:45:55.677 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=1年内不提交提案（按年度累计计分）, 子节点分数=0, 累计分数=0
11:45:55.677 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获立案的个人提案（第一提案人）, 策略键: proposal:accepted:primary, 是否有子节点: false
11:45:55.677 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=刘玉, 规则=获立案的个人提案（第一提案人）, 策略键=proposal:accepted:primary, 策略类型=BASIC, 方法=countApprovedProposalsAsPrimaryProposer
11:45:55.677 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=刘玉, 规则=获立案的个人提案（第一提案人）, 策略类型=BASIC, 方法名=countApprovedProposalsAsPrimaryProposer
11:45:55.677 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: countApprovedProposalsAsPrimaryProposer
11:45:55.677 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: countApprovedProposalsAsPrimaryProposer
11:45:55.678 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countApprovedProposalsAsPrimaryProposer - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM proposal p INNER JOIN proposal_user_rel pur ON p.id = pur.proposal_id AND pur.del_flag = false AND pur.proposer_id = ? AND pur.submit_type = '领衔' WHERE p.del_flag = false AND p.submit_type = 'INDIVIDUAL' AND p.year = ? AND p.case_filing IN ('PUT_ON', 'MERGED', 'WAIT_HANDLE', 'FINISH')
11:45:55.678 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countApprovedProposalsAsPrimaryProposer - [debug,135] - ==> Parameters: 203(String), 2024(String)
11:45:55.679 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countApprovedProposalsAsPrimaryProposer - [debug,135] - <==      Total: 1
11:45:55.679 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=刘玉, 方法=countApprovedProposalsAsPrimaryProposer, 分数=0
11:45:55.679 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=刘玉, 规则=获立案的个人提案（第一提案人）, 分数=0
11:45:55.679 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=获立案的个人提案（第一提案人）, 子节点分数=0, 累计分数=0
11:45:55.679 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获立案的个人提案（附议人）, 策略键: proposal:accepted:seconder, 是否有子节点: false
11:45:55.679 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=刘玉, 规则=获立案的个人提案（附议人）, 策略键=proposal:accepted:seconder, 策略类型=BASIC, 方法=countApprovedProposalsAsSecondaryProposer
11:45:55.679 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=刘玉, 规则=获立案的个人提案（附议人）, 策略类型=BASIC, 方法名=countApprovedProposalsAsSecondaryProposer
11:45:55.679 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: countApprovedProposalsAsSecondaryProposer
11:45:55.679 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: countApprovedProposalsAsSecondaryProposer
11:45:55.679 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=刘玉, 方法=countApprovedProposalsAsSecondaryProposer, 分数=0
11:45:55.680 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=刘玉, 规则=获立案的个人提案（附议人）, 分数=0
11:45:55.680 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=获立案的个人提案（附议人）, 子节点分数=0, 累计分数=0
11:45:55.680 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获重点提案（第一提案人）, 策略键: proposal:key:primary, 是否有子节点: false
11:45:55.680 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: proposal:key:primary
11:45:55.680 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=获重点提案（第一提案人）, 子节点分数=0, 累计分数=0
11:45:55.680 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获重点提案（附议人）, 策略键: proposal:key:seconder, 是否有子节点: false
11:45:55.680 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: proposal:key:seconder
11:45:55.680 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=获重点提案（附议人）, 子节点分数=0, 累计分数=0
11:45:55.680 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获区政协主席会议成员和区政府领导督办的提案（第一提案人）, 策略键: proposal:supervised:primary, 是否有子节点: false
11:45:55.680 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: proposal:supervised:primary
11:45:55.680 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=获区政协主席会议成员和区政府领导督办的提案（第一提案人）, 子节点分数=0, 累计分数=0
11:45:55.681 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获区政协主席会议成员和区政府领导督办的提案（附议人）, 策略键: proposal:supervised:seconder, 是否有子节点: false
11:45:55.681 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: proposal:supervised:seconder
11:45:55.681 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点=获区政协主席会议成员和区政府领导督办的提案（附议人）, 子节点分数=0, 累计分数=0
11:45:55.681 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=提交提案（以每件记，该提案分值取奖励分最高值）, 总分=0
11:45:55.681 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=基础分, 子节点=提交提案（以每件记，该提案分值取奖励分最高值）, 子节点分数=0, 累计分数=0
11:45:55.681 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 社情民意（以每篇计，该篇分值取奖励分最高值）, 策略键: null, 是否有子节点: true
11:45:55.681 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 1年内不提交社情民意（按年度累计计分）, 策略键: opinion:penalty:annual, 是否有子节点: false
11:45:55.681 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=刘玉, 规则=1年内不提交社情民意（按年度累计计分）, 策略键=opinion:penalty:annual, 策略类型=BASIC, 方法=hasNotSubmittedManuscriptWithinOneYear
11:45:55.681 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=刘玉, 规则=1年内不提交社情民意（按年度累计计分）, 策略类型=BASIC, 方法名=hasNotSubmittedManuscriptWithinOneYear
11:45:55.681 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: hasNotSubmittedManuscriptWithinOneYear
11:45:55.681 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: hasNotSubmittedManuscriptWithinOneYear
11:45:55.682 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.hasNotSubmittedManuscriptWithinOneYear - [debug,135] - ==>  Preparing: SELECT EXISTS ( SELECT 1 FROM proposal p INNER JOIN proposal_user_rel pur ON p.id = pur.proposal_id WHERE p.del_flag = false AND pur.del_flag = false AND pur.proposer_id = ? AND p.year = ? )
11:45:55.682 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.hasNotSubmittedManuscriptWithinOneYear - [debug,135] - ==> Parameters: 203(String), 2024(String)
11:45:55.683 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.hasNotSubmittedManuscriptWithinOneYear - [debug,135] - <==      Total: 1
11:45:55.683 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=刘玉, 方法=hasNotSubmittedManuscriptWithinOneYear, 分数=0
11:45:55.683 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=刘玉, 规则=1年内不提交社情民意（按年度累计计分）, 分数=0
11:45:55.684 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点=1年内不提交社情民意（按年度累计计分）, 子节点分数=0, 累计分数=0
11:45:55.684 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 社情民意被采纳, 策略键: opinion:accepted:basic, 是否有子节点: false
11:45:55.684 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=刘玉, 规则=社情民意被采纳, 策略键=opinion:accepted:basic, 策略类型=BASIC, 方法=countAcceptedManuscripts
11:45:55.684 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=刘玉, 规则=社情民意被采纳, 策略类型=BASIC, 方法名=countAcceptedManuscripts
11:45:55.684 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: countAcceptedManuscripts
11:45:55.684 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: countAcceptedManuscripts
11:45:55.684 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countAcceptedManuscripts - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM manuscript m INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false WHERE m.del_flag = false AND m.status != 'DISCARD' AND m.adopt_way != 'NOT_ADOPT' AND m.year = ? AND mrl.user_id = ?
11:45:55.685 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countAcceptedManuscripts - [debug,135] - ==> Parameters: 2024(String), 203(String)
11:45:55.686 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countAcceptedManuscripts - [debug,135] - <==      Total: 1
11:45:55.686 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=刘玉, 方法=countAcceptedManuscripts, 分数=0
11:45:55.686 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=刘玉, 规则=社情民意被采纳, 分数=0
11:45:55.686 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点=社情民意被采纳, 子节点分数=0, 累计分数=0
11:45:55.686 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 得到区领导批示, 策略键: opinion:endorsed:district, 是否有子节点: false
11:45:55.686 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=刘玉, 规则=得到区领导批示, 策略键=opinion:endorsed:district, 策略类型=BASIC, 方法=countDistrictEndorsedManuscript
11:45:55.686 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=刘玉, 规则=得到区领导批示, 策略类型=BASIC, 方法名=countDistrictEndorsedManuscript
11:45:55.686 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: countDistrictEndorsedManuscript
11:45:55.686 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: countDistrictEndorsedManuscript
11:45:55.687 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countDistrictEndorsedManuscript - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM manuscript m INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false INNER JOIN manuscript_endorsement me ON m.id = me.manuscript_id AND me.del_flag = false WHERE m.del_flag = false AND m.year = ? AND mrl.user_id = ? AND FIND_IN_SET('DISTRICT', me.endorse_type) > 0
11:45:55.687 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countDistrictEndorsedManuscript - [debug,135] - ==> Parameters: 2024(String), 203(String)
11:45:55.688 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countDistrictEndorsedManuscript - [debug,135] - <==      Total: 1
11:45:55.688 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=刘玉, 方法=countDistrictEndorsedManuscript, 分数=0
11:45:55.688 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=刘玉, 规则=得到区领导批示, 分数=0
11:45:55.688 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点=得到区领导批示, 子节点分数=0, 累计分数=0
11:45:55.688 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 得到市领导批示, 策略键: opinion:endorsed:municipal, 是否有子节点: false
11:45:55.690 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=刘玉, 规则=得到市领导批示, 策略键=opinion:endorsed:municipal, 策略类型=BASIC, 方法=countCityEndorsedManuscript
11:45:55.690 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=刘玉, 规则=得到市领导批示, 策略类型=BASIC, 方法名=countCityEndorsedManuscript
11:45:55.690 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: countCityEndorsedManuscript
11:45:55.690 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: countCityEndorsedManuscript
11:45:55.690 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countCityEndorsedManuscript - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM manuscript m INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false INNER JOIN manuscript_endorsement me ON m.id = me.manuscript_id AND me.del_flag = false WHERE m.del_flag = false AND m.year = ? AND mrl.user_id = ? AND FIND_IN_SET('CITY', me.endorse_type) > 0
11:45:55.690 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countCityEndorsedManuscript - [debug,135] - ==> Parameters: 2024(String), 203(String)
11:45:55.691 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countCityEndorsedManuscript - [debug,135] - <==      Total: 1
11:45:55.692 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=刘玉, 方法=countCityEndorsedManuscript, 分数=0
11:45:55.692 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=刘玉, 规则=得到市领导批示, 分数=0
11:45:55.692 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点=得到市领导批示, 子节点分数=0, 累计分数=0
11:45:55.692 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 被省政协采用, 策略键: opinion:adopted:province, 是否有子节点: false
11:45:55.692 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: opinion:adopted:province
11:45:55.692 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点=被省政协采用, 子节点分数=0, 累计分数=0
11:45:55.692 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 得到省领导批示, 策略键: opinion:endorsed:provincial, 是否有子节点: false
11:45:55.692 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,575] - 执行策略: 委员=刘玉, 规则=得到省领导批示, 策略键=opinion:endorsed:provincial, 策略类型=BASIC, 方法=countProvinceEndorsedManuscript
11:45:55.692 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,610] - 开始执行策略计算: 委员=刘玉, 规则=得到省领导批示, 策略类型=BASIC, 方法名=countProvinceEndorsedManuscript
11:45:55.692 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,620] - 执行基础策略: countProvinceEndorsedManuscript
11:45:55.692 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,623] - 调用方法: countProvinceEndorsedManuscript
11:45:55.693 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countProvinceEndorsedManuscript - [debug,135] - ==>  Preparing: SELECT COUNT(*) FROM manuscript m INNER JOIN manuscript_reflector_rel mrl ON m.id = mrl.manuscript_id AND mrl.del_flag = false INNER JOIN manuscript_endorsement me ON m.id = me.manuscript_id AND me.del_flag = false WHERE m.del_flag = false AND m.year = ? AND mrl.user_id = ? AND FIND_IN_SET('PROVINCE', me.endorse_type) > 0
11:45:55.693 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countProvinceEndorsedManuscript - [debug,135] - ==> Parameters: 2024(String), 203(String)
11:45:55.694 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.m.B.countProvinceEndorsedManuscript - [debug,135] - <==      Total: 1
11:45:55.694 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreByStrategyOriginal,626] - 基础策略执行完成: 委员=刘玉, 方法=countProvinceEndorsedManuscript, 分数=0
11:45:55.694 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,582] - 策略执行完成: 委员=刘玉, 规则=得到省领导批示, 分数=0
11:45:55.695 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点=得到省领导批示, 子节点分数=0, 累计分数=0
11:45:55.695 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 总分=0
11:45:55.695 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=基础分, 子节点=社情民意（以每篇计，该篇分值取奖励分最高值）, 子节点分数=0, 累计分数=0
11:45:55.695 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=基础分, 总分=0
11:45:55.695 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,482] - 顶级规则 基础分 最终分数: 0
11:45:55.695 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,487] - 累加基础分: 0
11:45:55.695 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,476] - 处理顶级规则: 奖励分
11:45:55.695 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 奖励分, 策略键: null, 是否有子节点: true
11:45:55.695 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 撰写调研报告或其它文字资料（以每篇计）, 策略键: null, 是否有子节点: true
11:45:55.695 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 调研报告被采用（执笔者）, 策略键: research_report:adopted:writer, 是否有子节点: false
11:45:55.695 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: research_report:adopted:writer
11:45:55.695 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=调研报告被采用（执笔者）, 子节点分数=0, 累计分数=0
11:45:55.696 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 调研报告被采用（参与者）, 策略键: research_report:adopted:participant, 是否有子节点: false
11:45:55.696 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: research_report:adopted:participant
11:45:55.696 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=调研报告被采用（参与者）, 子节点分数=0, 累计分数=0
11:45:55.696 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 调研报告被奖励（执笔者）, 策略键: research_report:rewarded:writer, 是否有子节点: false
11:45:55.696 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: research_report:rewarded:writer
11:45:55.696 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=调研报告被奖励（执笔者）, 子节点分数=0, 累计分数=0
11:45:55.696 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 调研报告被奖励（参与者）, 策略键: research_report:rewarded:participant, 是否有子节点: false
11:45:55.696 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: research_report:rewarded:participant
11:45:55.696 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=调研报告被奖励（参与者）, 子节点分数=0, 累计分数=0
11:45:55.696 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 调研报告被区领导批示（执笔者）, 策略键: research_report:endorsed_district:writer, 是否有子节点: false
11:45:55.696 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: research_report:endorsed_district:writer
11:45:55.696 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=调研报告被区领导批示（执笔者）, 子节点分数=0, 累计分数=0
11:45:55.696 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 调研报告被区领导批示（参与者）, 策略键: research_report:endorsed_district:participant, 是否有子节点: false
11:45:55.696 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: research_report:endorsed_district:participant
11:45:55.697 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=调研报告被区领导批示（参与者）, 子节点分数=0, 累计分数=0
11:45:55.697 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 发表文章（被采纳、刊登、刊用）, 策略键: article:published, 是否有子节点: false
11:45:55.697 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: article:published
11:45:55.697 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=发表文章（被采纳、刊登、刊用）, 子节点分数=0, 累计分数=0
11:45:55.697 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 发表文章（获奖）, 策略键: article:awarded, 是否有子节点: false
11:45:55.697 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: article:awarded
11:45:55.697 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=撰写调研报告或其它文字资料（以每篇计）, 子节点=发表文章（获奖）, 子节点分数=0, 累计分数=0
11:45:55.697 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=撰写调研报告或其它文字资料（以每篇计）, 总分=0
11:45:55.697 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=奖励分, 子节点=撰写调研报告或其它文字资料（以每篇计）, 子节点分数=0, 累计分数=0
11:45:55.697 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加区政协年度重点工作, 策略键: null, 是否有子节点: true
11:45:55.697 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动（特别突出）, 策略键: env_protection:supervision:outstanding, 是否有子节点: false
11:45:55.698 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: env_protection:supervision:outstanding
11:45:55.698 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.698 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动（表现较好）, 策略键: env_protection:supervision:good, 是否有子节点: false
11:45:55.698 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: env_protection:supervision:good
11:45:55.698 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.698 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动（有所参与）, 策略键: env_protection:supervision:participated, 是否有子节点: false
11:45:55.698 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: env_protection:supervision:participated
11:45:55.698 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参与“六个严禁两个推进”及水环境治理等生态环保民主监督活动（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.698 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加脱贫攻坚与乡村振兴工作（特别突出）, 策略键: rural_revitalization:outstanding, 是否有子节点: false
11:45:55.698 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: rural_revitalization:outstanding
11:45:55.698 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加脱贫攻坚与乡村振兴工作（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.698 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加脱贫攻坚与乡村振兴工作（表现较好）, 策略键: rural_revitalization:good, 是否有子节点: false
11:45:55.699 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: rural_revitalization:good
11:45:55.699 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加脱贫攻坚与乡村振兴工作（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.699 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加脱贫攻坚与乡村振兴工作（有所参与）, 策略键: rural_revitalization:participated, 是否有子节点: false
11:45:55.699 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: rural_revitalization:participated
11:45:55.699 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加脱贫攻坚与乡村振兴工作（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.699 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（特别突出）, 策略键: grassroots_democracy:outstanding, 是否有子节点: false
11:45:55.699 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: grassroots_democracy:outstanding
11:45:55.699 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.699 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（表现较好）, 策略键: grassroots_democracy:good, 是否有子节点: false
11:45:55.699 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: grassroots_democracy:good
11:45:55.699 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.699 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（有所参与）, 策略键: grassroots_democracy:participated, 是否有子节点: false
11:45:55.700 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: grassroots_democracy:participated
11:45:55.700 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.700 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加书香政协学习、宣讲活动（特别突出）, 策略键: reading_activities:outstanding, 是否有子节点: false
11:45:55.700 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: reading_activities:outstanding
11:45:55.700 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加书香政协学习、宣讲活动（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.700 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加书香政协学习、宣讲活动（表现较好）, 策略键: reading_activities:good, 是否有子节点: false
11:45:55.700 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: reading_activities:good
11:45:55.700 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加书香政协学习、宣讲活动（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.700 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加书香政协学习、宣讲活动（有所参与）, 策略键: reading_activities:participated, 是否有子节点: false
11:45:55.700 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: reading_activities:participated
11:45:55.700 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加书香政协学习、宣讲活动（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.700 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加新冠肺炎疫情防控（特别突出）, 策略键: epidemic_prevention:outstanding, 是否有子节点: false
11:45:55.700 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: epidemic_prevention:outstanding
11:45:55.700 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加新冠肺炎疫情防控（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.700 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加新冠肺炎疫情防控（表现较好）, 策略键: epidemic_prevention:good, 是否有子节点: false
11:45:55.700 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: epidemic_prevention:good
11:45:55.700 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加新冠肺炎疫情防控（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.700 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 参加新冠肺炎疫情防控（有所参与）, 策略键: epidemic_prevention:participated, 是否有子节点: false
11:45:55.702 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: epidemic_prevention:participated
11:45:55.702 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=参加新冠肺炎疫情防控（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.702 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 助力项目建设服务，参加引进外资活动（特别突出）, 策略键: project_service:outstanding, 是否有子节点: false
11:45:55.702 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: project_service:outstanding
11:45:55.702 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=助力项目建设服务，参加引进外资活动（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.702 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 助力项目建设服务，参加引进外资活动（表现较好）, 策略键: project_service:good, 是否有子节点: false
11:45:55.702 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: project_service:good
11:45:55.702 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=助力项目建设服务，参加引进外资活动（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.702 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 助力项目建设服务，参加引进外资活动（有所参与）, 策略键: project_service:participated, 是否有子节点: false
11:45:55.702 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: project_service:participated
11:45:55.702 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=助力项目建设服务，参加引进外资活动（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.703 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 助推创一流营商环境（特别突出）, 策略键: business_environment:outstanding, 是否有子节点: false
11:45:55.703 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: business_environment:outstanding
11:45:55.703 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=助推创一流营商环境（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.703 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 助推创一流营商环境（表现较好）, 策略键: business_environment:good, 是否有子节点: false
11:45:55.703 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: business_environment:good
11:45:55.703 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=助推创一流营商环境（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.703 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 助推创一流营商环境（有所参与）, 策略键: business_environment:participated, 是否有子节点: false
11:45:55.703 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: business_environment:participated
11:45:55.703 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=助推创一流营商环境（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.703 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 完成区政协交办的其他任务（特别突出）, 策略键: other_tasks:outstanding, 是否有子节点: false
11:45:55.703 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: other_tasks:outstanding
11:45:55.703 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=完成区政协交办的其他任务（特别突出）, 子节点分数=0, 累计分数=0
11:45:55.704 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 完成区政协交办的其他任务（表现较好）, 策略键: other_tasks:good, 是否有子节点: false
11:45:55.704 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: other_tasks:good
11:45:55.704 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=完成区政协交办的其他任务（表现较好）, 子节点分数=0, 累计分数=0
11:45:55.704 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 完成区政协交办的其他任务（有所参与）, 策略键: other_tasks:participated, 是否有子节点: false
11:45:55.704 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: other_tasks:participated
11:45:55.704 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=参加区政协年度重点工作, 子节点=完成区政协交办的其他任务（有所参与）, 子节点分数=0, 累计分数=0
11:45:55.704 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=参加区政协年度重点工作, 总分=0
11:45:55.704 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=奖励分, 子节点=参加区政协年度重点工作, 子节点分数=0, 累计分数=0
11:45:55.704 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获得奖励（如优秀政协委员、道德模范等，以次数计）, 策略键: null, 是否有子节点: true
11:45:55.704 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获得奖励（区级）, 策略键: awards:district, 是否有子节点: false
11:45:55.704 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: awards:district
11:45:55.704 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=获得奖励（如优秀政协委员、道德模范等，以次数计）, 子节点=获得奖励（区级）, 子节点分数=0, 累计分数=0
11:45:55.704 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获得奖励（市级）, 策略键: awards:municipal, 是否有子节点: false
11:45:55.705 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: awards:municipal
11:45:55.705 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=获得奖励（如优秀政协委员、道德模范等，以次数计）, 子节点=获得奖励（市级）, 子节点分数=0, 累计分数=0
11:45:55.705 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获得奖励（省级）, 策略键: awards:provincial, 是否有子节点: false
11:45:55.705 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: awards:provincial
11:45:55.705 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=获得奖励（如优秀政协委员、道德模范等，以次数计）, 子节点=获得奖励（省级）, 子节点分数=0, 累计分数=0
11:45:55.705 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 获得奖励（国家级）, 策略键: awards:national, 是否有子节点: false
11:45:55.705 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: awards:national
11:45:55.705 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=获得奖励（如优秀政协委员、道德模范等，以次数计）, 子节点=获得奖励（国家级）, 子节点分数=0, 累计分数=0
11:45:55.705 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=获得奖励（如优秀政协委员、道德模范等，以次数计）, 总分=0
11:45:55.705 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=奖励分, 子节点=获得奖励（如优秀政协委员、道德模范等，以次数计）, 子节点分数=0, 累计分数=0
11:45:55.705 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 办好事、解难题、做公益情况, 策略键: null, 是否有子节点: true
11:45:55.705 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,558] - 评估规则节点: 以政协委员身份为人民群众办好事、解难题、做公益慈善等贡献突出的1次, 策略键: public_welfare:outstanding, 是否有子节点: false
11:45:55.706 [http-nio-8080-exec-1] WARN  c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,571] - 未找到策略映射，策略键: public_welfare:outstanding
11:45:55.706 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=办好事、解难题、做公益情况, 子节点=以政协委员身份为人民群众办好事、解难题、做公益慈善等贡献突出的1次, 子节点分数=0, 累计分数=0
11:45:55.706 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=办好事、解难题、做公益情况, 总分=0
11:45:55.706 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,594] - 子节点分数汇总: 父节点=奖励分, 子节点=办好事、解难题、做公益情况, 子节点分数=0, 累计分数=0
11:45:55.706 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [evaluateLeafNodesOriginal,598] - 非叶子节点分数设置完成: 节点=奖励分, 总分=0
11:45:55.706 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,482] - 顶级规则 奖励分 最终分数: 0
11:45:55.706 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,491] - 累加奖励分: 0
11:45:55.706 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,495] - 委员 刘玉 计算完成: 基础分=0, 奖励分=0, 总分=0
11:45:55.706 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScoreOriginalFixed,529] - 修复版原始计算完成，数据量: 192
11:45:55.707 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScore,121] - 计算规则分数完成，耗时：168 ms
11:45:55.708 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScore,125] - 计算完成，有分数的委员数量: 0/192
11:45:55.709 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [upsertRuleScoresBatch,865] - 开始批量保存规则分数，数据量: 192
11:45:55.709 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [lambda$upsertRuleScoresBatch$6,891] - 委员详情: ID=1902273680180912481, 姓名=黄旭, 基础分=0, 奖励分=0, 总分=0
11:45:55.709 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [lambda$upsertRuleScoresBatch$6,891] - 委员详情: ID=1902273680180912482, 姓名=李晓松, 基础分=0, 奖励分=0, 总分=0
11:45:55.709 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [lambda$upsertRuleScoresBatch$6,891] - 委员详情: ID=1902273680180912483, 姓名=刘玉, 基础分=0, 奖励分=0, 总分=0
11:45:55.709 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [upsertRuleScoresBatch,895] - 实际处理数据量: 192
11:45:55.715 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [upsertRuleScoresBatch,903] - 分批处理，批次数: 4, 每批大小: 50
11:45:55.778 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [upsertRuleScoresBatch,915] - 批次 1/4 完成，MySQL影响行数: 50, 实际处理记录数: 50
11:45:55.821 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [upsertRuleScoresBatch,915] - 批次 2/4 完成，MySQL影响行数: 50, 实际处理记录数: 50
11:45:55.862 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [upsertRuleScoresBatch,915] - 批次 3/4 完成，MySQL影响行数: 50, 实际处理记录数: 50
11:45:55.892 [http-nio-8080-exec-1] DEBUG c.r.p.c.e.s.i.RuleScoreServiceImpl - [upsertRuleScoresBatch,915] - 批次 4/4 完成，MySQL影响行数: 42, 实际处理记录数: 42
11:45:55.892 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [upsertRuleScoresBatch,932] - 批量保存完成，总影响行数: 192
11:45:55.892 [http-nio-8080-exec-1] INFO  c.r.p.c.e.s.i.RuleScoreServiceImpl - [calculateScore,130] - 保存规则分数完成，耗时：352 ms