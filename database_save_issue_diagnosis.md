# 数据库保存分数为0问题诊断

## 🚨 问题现象

### **症状描述**
- **计算阶段**: 日志显示有57个委员有分数
- **保存后**: 数据库中所有分数字段都是0
- **说明**: 问题出现在数据保存阶段，而不是计算阶段

```
18:15:32.209 [http-nio-8080-exec-2] INFO - 计算完成，有分数的委员数量: 57/192
数据库查询结果: basic_score=0, reward_score=0, total_score=0 (所有记录)
```

## 🔍 可能的原因分析

### **1. SQL映射问题** ⭐ **最可能的原因**
- **XML字段缺失**: 原始SQL缺少必要的字段（create_time, update_time, del_flag）
- **字段映射错误**: 可能存在字段名不匹配的问题

### **2. 数据类型问题**
- **NULL值处理**: Integer类型的分数字段可能被设置为NULL
- **默认值覆盖**: 数据库默认值可能覆盖了计算结果

### **3. 事务问题**
- **事务回滚**: 虽然不太可能，但可能存在隐式回滚
- **并发问题**: 多线程访问导致数据覆盖

### **4. 对象状态问题**
- **对象引用**: RuleScore对象在保存前被意外修改
- **序列化问题**: 对象在传递过程中丢失数据

## ✅ 已实施的修复措施

### **1. 增强调试日志**
```java
// 计算完成后的日志
if (totalScore > 0) {
    log.info("委员 {} 计算完成: 基础分={}, 奖励分={}, 总分={}", 
            member.getUserName(), basicScore, rewardScore, totalScore);
}

// RuleScore对象创建后的日志
if (totalScore > 0) {
    log.info("RuleScore对象创建: 委员={}, 基础分={}, 奖励分={}, 总分={}", 
            ruleScore.getUserName(), ruleScore.getBasicScore(), 
            ruleScore.getRewardScore(), ruleScore.getTotalScore());
}

// 保存前的统计日志
long nonZeroCount = uniqueList.stream().mapToInt(RuleScore::getTotalScore).filter(score -> score > 0).count();
log.info("保存前统计: 总数={}, 有分数的委员数={}", uniqueList.size(), nonZeroCount);
```

### **2. 修复SQL映射**
```xml
<!-- 修复前：缺少必要字段 -->
INSERT INTO rule_score (id, year, user_id, ..., score_detail) VALUES ...

<!-- 修复后：包含所有必要字段 -->
INSERT INTO rule_score (
    id, year, user_id, ..., score_detail,
    create_time, update_time, del_flag
) VALUES (
    #{item.id}, ..., #{item.scoreDetail},
    NOW(), NOW(), 0
)
```

### **3. 完善ON DUPLICATE KEY UPDATE**
```xml
ON DUPLICATE KEY UPDATE
    basic_score = VALUES(basic_score),
    reward_score = VALUES(reward_score),
    total_score = VALUES(total_score),
    score_detail = VALUES(score_detail),
    update_time = NOW()
```

## 🔧 诊断步骤

### **第一步：运行修复后的代码**
查看新增的调试日志，确认：
1. 计算阶段的分数是否正确
2. RuleScore对象创建时分数是否正确
3. 保存前的分数统计是否正确

### **第二步：检查日志输出**
预期看到的日志：
```
委员 张三 计算完成: 基础分=25, 奖励分=10, 总分=35
RuleScore对象创建: 委员=张三, 基础分=25, 奖励分=10, 总分=35
保存前统计: 总数=192, 有分数的委员数=57
保存前有分数委员: ID=123, 姓名=张三, 基础分=25, 奖励分=10, 总分=35
```

### **第三步：数据库验证**
```sql
-- 检查保存后的数据
SELECT id, user_name, basic_score, reward_score, total_score, create_time, update_time
FROM rule_score 
WHERE year = 2024 
AND (basic_score > 0 OR reward_score > 0 OR total_score > 0)
ORDER BY total_score DESC
LIMIT 10;
```

## 🎯 可能的问题场景

### **场景1：SQL字段映射错误**
**症状**: 日志显示分数正确，但数据库为0
**原因**: XML中字段名与数据库列名不匹配
**解决**: 检查XML中的字段映射

### **场景2：MyBatis参数传递问题**
**症状**: 批量插入时参数丢失
**原因**: @Param注解与XML中collection名称不匹配
**解决**: 确保参数名一致

### **场景3：数据库约束问题**
**症状**: 插入成功但分数被重置
**原因**: 数据库触发器或约束重置了分数
**解决**: 检查数据库表结构和约束

### **场景4：对象状态问题**
**症状**: 对象在保存前被修改
**原因**: 并发访问或意外的对象修改
**解决**: 通过日志确认对象状态

## ⚠️ 紧急排查清单

### **立即检查**
- [ ] 运行修复后的代码，查看详细日志
- [ ] 确认计算阶段的分数是否正确
- [ ] 确认RuleScore对象创建时的分数
- [ ] 确认保存前的分数统计

### **数据库检查**
- [ ] 检查rule_score表结构
- [ ] 确认字段类型和约束
- [ ] 查看是否有触发器影响数据

### **代码检查**
- [ ] 确认XML映射的正确性
- [ ] 检查@Param注解的一致性
- [ ] 验证对象传递过程

## 📊 预期修复效果

### **如果是SQL映射问题**
- ✅ 修复后数据库中应该有正确的分数
- ✅ 日志显示保存前后分数一致

### **如果是其他问题**
- 🔍 通过详细日志定位具体问题点
- 🔧 根据日志信息进行针对性修复

## 总结

通过增强调试日志和修复SQL映射，应该能够快速定位问题所在。关键是要确认数据在哪个环节丢失：

1. **计算阶段** → **对象创建阶段** → **保存前** → **数据库**

通过逐步验证每个环节，就能找到分数丢失的具体原因。
