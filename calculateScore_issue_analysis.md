# calculateScore 方法问题分析与修复

## 问题现象
优化后的 `calculateScore` 方法计算出的委员分数都为 0，而原始方法能正确计算出分数。

## 问题分析

### 1. 可能的根本原因

#### A. 后序遍历逻辑错误
**问题**: 原始的迭代后序遍历实现有逻辑错误
```java
// 有问题的实现
while (!stack.isEmpty()) {
    RuleDetailVo current = stack.peek();
    if (ObjectUtil.isEmpty(current.getChildren()) || visited.contains(current)) {
        postOrder.add(stack.pop());
        visited.add(current); // 这里会导致重复添加到visited
    } else {
        // 子节点处理逻辑
        visited.add(current); // 过早标记为已访问
    }
}
```

**影响**: 节点遍历顺序错误，导致分数计算不正确

#### B. 策略方法映射构建失败
**问题**: 预编译的策略方法映射可能构建失败或不完整
- 反射方法获取失败
- 策略类型匹配错误
- 方法签名不匹配

#### C. 深拷贝导致字段丢失
**问题**: JSON 深拷贝可能丢失某些关键字段
```java
RuleInfoVo memberRuleInfo = JSON.parseObject(JSON.toJSONString(ruleInfo), RuleInfoVo.class);
```

**影响**: 策略键(strategyKey)等关键字段可能丢失

#### D. 并行处理的线程安全问题
**问题**: 并行流处理时可能存在共享状态修改冲突

## 修复方案

### 1. 立即修复 - 回退到原始逻辑
```java
// 暂时使用原始逻辑确保功能正常
List<RuleScore> ruleScoreList = calculateScoreOriginal(memberList, ruleInfo, strategyMap, ruleInfoJson);
```

### 2. 逐步调试和修复

#### 步骤1: 验证策略映射构建
```java
// 添加详细日志
log.info("策略映射数量: {}", strategyMap.size());
log.info("成功构建策略方法映射，数量: {}", strategyMethodMap.size());

// 检查关键策略是否存在
for (String key : strategyMap.keySet()) {
    if (!strategyMethodMap.containsKey(key)) {
        log.error("策略方法映射缺失: {}", key);
    }
}
```

#### 步骤2: 验证规则树遍历
```java
// 添加遍历日志
log.debug("处理规则: {}, 策略键: {}", ruleDetailVo.getRemark(), ruleDetailVo.getStrategyKey());
log.debug("规则 {} 最终分数: {}", ruleDetailVo.getRemark(), finalScore);
```

#### 步骤3: 对比原始和优化逻辑
```java
// 同时运行两种逻辑，对比结果
RuleScore originalScore = calculateMemberScoreOriginal(member, ruleInfo, strategyMap, ruleInfoJson);
RuleScore optimizedScore = calculateMemberScore(member, ruleInfo, strategyMethodMap, ruleInfoJson);

if (!originalScore.getTotalScore().equals(optimizedScore.getTotalScore())) {
    log.error("分数计算不一致: 原始={}, 优化={}", originalScore.getTotalScore(), optimizedScore.getTotalScore());
}
```

### 3. 修复后的优化策略

#### A. 修复递归遍历
```java
// 暂时回退到递归实现，确保正确性
private void evaluateLeafNodesOptimized(CommitteeMember member, RuleDetailVo ruleDetail,
                                      Map<String, BiFunction<CommitteeMember, RuleDetailVo, Integer>> strategyMethodMap) {
    if (ruleDetail == null) return;

    if (ObjectUtil.isEmpty(ruleDetail.getChildren())) {
        // 叶子节点：计算分数
        calculateLeafNodeScore(member, ruleDetail, strategyMethodMap);
    } else {
        // 非叶子节点：递归计算子节点，然后汇总
        int childrenTotalScore = 0;
        for (RuleDetailVo child : ruleDetail.getChildren()) {
            evaluateLeafNodesOptimized(member, child, strategyMethodMap);
            childrenTotalScore += Optional.ofNullable(child.getFinalScore()).orElse(0);
        }
        ruleDetail.setFinalScore(childrenTotalScore);
    }
}
```

#### B. 增强错误处理
```java
// 策略执行失败时的降级处理
try {
    Integer score = strategyFunction.apply(member, ruleDetail);
    ruleDetail.setFinalScore(score);
} catch (Exception e) {
    log.error("策略执行失败，使用原始方法: key={}, member={}", 
             ruleDetail.getStrategyKey(), member.getUserName(), e);
    // 降级到原始方法
    fallbackToOriginalStrategy(member, ruleDetail, strategyMap);
}
```

#### C. 分阶段优化
1. **第一阶段**: 只优化策略实例缓存，保持原有逻辑
2. **第二阶段**: 优化反射方法缓存
3. **第三阶段**: 引入并行处理
4. **第四阶段**: 优化递归遍历

## 当前状态

### 已实施的修复
1. ✅ 添加详细的调试日志
2. ✅ 回退到原始计算逻辑
3. ✅ 保留优化代码用于后续调试
4. ✅ 修复递归遍历逻辑

### 待验证的问题
1. 🔍 策略方法映射是否完整构建
2. 🔍 深拷贝是否丢失关键字段
3. 🔍 反射方法调用是否正确执行

### 下一步行动
1. **运行测试**: 使用原始逻辑验证功能正常
2. **日志分析**: 查看详细日志找出具体问题点
3. **逐步优化**: 一次只启用一个优化点进行测试
4. **性能对比**: 确认优化效果后再全面启用

## 建议的测试步骤

### 1. 功能验证
```bash
# 运行分数计算，检查日志输出
# 确认委员分数不再为0
```

### 2. 日志分析
```bash
# 查看关键日志点：
# - 策略映射构建数量
# - 策略方法映射数量  
# - 每个委员的分数计算结果
# - 任何错误或警告信息
```

### 3. 逐步启用优化
```java
// 第一步：只启用策略实例缓存
// 第二步：启用方法缓存
// 第三步：启用并行处理
// 每步都验证结果正确性
```

## 总结

当前已回退到稳定的原始逻辑，确保功能正常。通过详细的日志和对比测试，可以逐步识别和修复优化代码中的问题，最终实现既正确又高效的分数计算逻辑。
