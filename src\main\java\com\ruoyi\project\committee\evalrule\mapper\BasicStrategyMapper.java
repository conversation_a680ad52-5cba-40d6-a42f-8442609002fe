package com.ruoyi.project.committee.evalrule.mapper;

import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface BasicStrategyMapper {

    /**
     * 一年内是否未提交提案
     * @param member member
     * @return result
     */
    Boolean hasNotSubmittedProposalWithinOneYear(@Param("member") CommitteeMember  member);

    /**
     * 获取委员获立案的提案数量（第一提案人）
     * @param member member
     * @return result
     */
    Integer countApprovedProposalsAsPrimaryProposer(@Param("member") CommitteeMember  member);

    /**
     * 获取委员获立案的提案数量（附议人）
     * @param member member
     * @return result
     */
    Integer countApprovedProposalsAsSecondaryProposer(@Param("member") CommitteeMember  member);

    /**
     * 一年内是否未提交社情民意
     * @param member member
     * @return result
     */
    Boolean hasNotSubmittedManuscriptWithinOneYear(@Param("member") CommitteeMember  member);

    /**
     * 获取委员被采纳的社情民意数量
     * @param member member
     * @return result
     */
    Integer countAcceptedManuscripts(@Param("member") CommitteeMember member);

    /**
     * 获取得到区领导批示的稿件数量
     * @return result
     */
    Integer countDistrictEndorsedManuscript(@Param("member") CommitteeMember member);

    /**
     * 获取得到市领导批示的稿件数量
     * @return result
     */
    Integer countCityEndorsedManuscript(@Param("member") CommitteeMember member);

    /**
     * 获取得到省领导批示的稿件数量
     * @return result
     */
    Integer countProvinceEndorsedManuscript(@Param("member") CommitteeMember member);
}
