package com.ruoyi.project.committee.evalrule.mapper;

import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface BasicStrategyMapper {

    /**
     * 缺席区政协全体会议1次（未请假）
     *
     * @param member member
     */
    Integer handleAbsencePlenaryUnexcused(@Param("member") CommitteeMember member);

    /**
     * 批量查询所有委员缺席区政协全体会议（未请假）的次数
     *
     * @param year 年份
     * @return Map<委员ID, 缺席次数>
     */
    Map<Long, Integer> batchHandleAbsencePlenaryUnexcused(@Param("year") String year);

    /**
     * 缺席区政协全体会议1次（请假）
     *
     * @param member member
     */
    Integer handleAbsencePlenaryExcused(@Param("member") CommitteeMember member);

    /**
     * 批量查询所有委员缺席区政协全体会议（请假）的次数
     *
     * @param year 年份
     * @return Map<委员ID, 缺席次数>
     */
    Map<Long, Integer> batchHandleAbsencePlenaryExcused(@Param("year") String year);

    /**
     * 缺席区政协常委会议1次（未请假）
     * 
     * @param member member
     */
    Integer handleAbsenceStandingUnexcused(@Param("member") CommitteeMember member);

    /**
     * 缺席区政协常委会议1次（请假）
     * 
     * @param member member
     */
    Integer handleAbsenceStandingExcused(@Param("member") CommitteeMember member);

    /**
     * 缺席区政协专委会会议1次（未请假）
     * 
     * @param member member
     */
    Integer handleAbsenceCommitteeUnexcused(@Param("member") CommitteeMember member);

    /**
     * 缺席区政协专委会会议1次（请假）
     * 
     * @param member member
     */
    Integer handleAbsenceCommitteeExcused(@Param("member") CommitteeMember member);

    /**
     * 区政协委员列席区政协常委扩大会议
     * 
     * @param member member
     */
    Integer handleAttendanceStandingExtended(@Param("member") CommitteeMember member);

    /**
     * 参加专委会组织的其他会议（非全体会议）
     * 
     * @param member member
     */
    Integer handleAttendanceCommitteeOther(@Param("member") CommitteeMember member);

    /**
     * 参加全国政协、省政协、市政协组织的相关会议
     * 
     * @param member member
     */
    Integer handleAttendanceHigherMeeting(@Param("member") CommitteeMember member);

    /**
     * 参加全国政协、省政协、市政协、区政协组织相关活动
     * 
     * @param member 政协委员对象
     */
    Integer handleAttendanceMultiActivity(@Param("member") CommitteeMember member);

    /**
     * 受区政协委托参加区委、区政府组成部门等单位组织的活动
     * 
     * @param member 政协委员对象
     *               remark >> 没有该类型的活动
     */
    // Integer handleAttendanceDelegatedExternal(@Param("member") CommitteeMember
    // member);

    /**
     * 参加区政协组织的委员培训活动
     * 
     * @param member 政协委员对象
     *               remark >> 没有该类型的活动
     */
    // Integer handleTrainingDistrictRegular(@Param("member") CommitteeMember
    // member);

    /**
     * 参加与区政协工作相关的各类会议与活动情况
     * 
     * @param member 政协委员对象
     */
    Integer handleParticipationRelatedAll(@Param("member") CommitteeMember member);

    /**
     * 处理全会/常委会/联组会议的书面+口头发言
     * 
     * @param member 政协委员对象
     */
    Integer handleSpeechPlenaryBoth(@Param("member") CommitteeMember member);

    /**
     * 处理全会/常委会/联组会议的书面发言
     * 
     * @param member 政协委员对象
     */
    Integer handleSpeechPlenaryWritten(@Param("member") CommitteeMember member);

    /**
     * 处理其他会议的发言（按次数计）
     * 
     * @param member 政协委员对象
     */
    Integer handleSpeechOtherCounted(@Param("member") CommitteeMember member);

    /**
     * 一年内是否未提交提案
     * 
     * @param member member
     * @return result
     */
    Boolean hasNotSubmittedProposalWithinOneYear(@Param("member") CommitteeMember member);

    /**
     * 获取委员获立案的提案数量（第一提案人）
     * 
     * @param member member
     * @return result
     */
    Integer countApprovedProposalsAsPrimaryProposer(@Param("member") CommitteeMember member);

    /**
     * 获取委员获立案的提案数量（附议人）
     * 
     * @param member member
     * @return result
     */
    Integer countApprovedProposalsAsSecondaryProposer(@Param("member") CommitteeMember member);

    /**
     * 一年内是否未提交社情民意
     * 
     * @param member member
     * @return result
     */
    Boolean hasNotSubmittedManuscriptWithinOneYear(@Param("member") CommitteeMember member);

    /**
     * 获取委员被采纳的社情民意数量
     * 
     * @param member member
     * @return result
     */
    Integer countAcceptedManuscripts(@Param("member") CommitteeMember member);

    /**
     * 获取得到区领导批示的稿件数量
     * 
     * @return result
     */
    Integer countDistrictEndorsedManuscript(@Param("member") CommitteeMember member);

    /**
     * 获取得到市领导批示的稿件数量
     * 
     * @return result
     */
    Integer countCityEndorsedManuscript(@Param("member") CommitteeMember member);

    /**
     * 获取得到省领导批示的稿件数量
     *
     * @return result
     */
    Integer countProvinceEndorsedManuscript(@Param("member") CommitteeMember member);

    // ==================== 批量查询方法 ====================

    /**
     * 批量查询所有委员的基础分数据
     * 返回Map结构：委员ID -> 方法名 -> 分数
     * 
     * @param memberList 委员列表
     * @return 批量查询结果
     */
    Map<String, Map<String, Integer>> batchQueryAllBasicScores(@Param("memberList") List<CommitteeMember> memberList);

    /**
     * 批量查询指定方法的所有委员数据
     * 返回Map结构：委员ID -> 分数
     * 
     * @param memberList 委员列表
     * @param methodName 方法名
     * @return 批量查询结果
     */
    Map<String, Integer> batchQueryByMethod(@Param("memberList") List<CommitteeMember> memberList,
            @Param("methodName") String methodName);
}
