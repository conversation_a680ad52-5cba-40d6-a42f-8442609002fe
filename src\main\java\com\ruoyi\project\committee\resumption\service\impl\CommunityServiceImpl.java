package com.ruoyi.project.committee.resumption.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.domain.dto.PageDTO;
import com.ruoyi.common.enums.AuditStatusEnum;
import com.ruoyi.common.enums.CommunityTypeEnum;
import com.ruoyi.common.enums.committee.PersonTypeEnum;
import com.ruoyi.common.enums.committee.ReportTypeEnum;
import com.ruoyi.common.utils.CollUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.committee.archive.domain.dto.CommitteeMemberQueryDTO;
import com.ruoyi.project.committee.archive.domain.vo.CommitteeMemberQueryVo;
import com.ruoyi.project.committee.archive.service.ICommitteeMemberService;
import com.ruoyi.project.committee.resumption.domain.dto.CommunityAddDTO;
import com.ruoyi.project.committee.resumption.domain.dto.CommunityAnnexDTO;
import com.ruoyi.project.committee.resumption.domain.dto.CommunityAuditDTO;
import com.ruoyi.project.committee.resumption.domain.dto.CommunityUpdateDTO;
import com.ruoyi.project.committee.resumption.domain.po.Community;
import com.ruoyi.project.committee.resumption.domain.po.CommunityAnnex;
import com.ruoyi.project.committee.resumption.domain.po.ExamineParticipant;
import com.ruoyi.project.committee.resumption.domain.po.Honor;
import com.ruoyi.project.committee.resumption.domain.query.CommunityPageQuery;
import com.ruoyi.project.committee.resumption.domain.query.CommunityUserPageQuery;
import com.ruoyi.project.committee.resumption.domain.vo.CommunityAnnexVo;
import com.ruoyi.project.committee.resumption.domain.vo.CommunityDetailVo;
import com.ruoyi.project.committee.resumption.domain.vo.CommunityPageVo;
import com.ruoyi.project.committee.resumption.domain.vo.ParticipantVo;
import com.ruoyi.project.committee.resumption.mapper.CommunityMapper;
import com.ruoyi.project.committee.resumption.service.ICommunityAnnexService;
import com.ruoyi.project.committee.resumption.service.ICommunityService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 公益情况Service实现
 */
@Service
@RequiredArgsConstructor
public class CommunityServiceImpl extends ServiceImpl<CommunityMapper, Community> implements ICommunityService {

    private final ICommunityAnnexService communityAnnexService;
    private final ICommitteeMemberService committeeMemberService;
    private final ExamineParticipantService examineParticipantService;

    /**
     * 处理日期结束时间，如果是零点，则设置为当天的最后一毫秒
     *
     * @param date 原始日期
     * @return 处理后的日期
     */
    private Date adjustEndTime(Date date) {
        if (date == null) {
            return null;
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        if (cal.get(Calendar.HOUR_OF_DAY) == 0
                && cal.get(Calendar.MINUTE) == 0
                && cal.get(Calendar.SECOND) == 0
                && cal.get(Calendar.MILLISECOND) == 0) {
            cal.add(Calendar.DATE, 1);
            cal.add(Calendar.MILLISECOND, -1);
            return cal.getTime();
        }

        return date;
    }

    @Override
    public PageDTO<CommunityPageVo> getCommunityList(CommunityPageQuery query) {
        query.setPageNo(query.getCurrentPage());
        Date endTime = adjustEndTime(query.getEndTime());

        Page<Community> page = lambdaQuery()
                .like(StringUtils.isNotEmpty(query.getTitle()), Community::getTitle, query.getTitle())
                .like(StringUtils.isNotEmpty(query.getParticipantsName()), Community::getParticipantsName, query.getParticipantsName())
                .eq(StringUtils.isNotEmpty(query.getAuditStatus()), Community::getAudiStatus, AuditStatusEnum.fromValue(query.getAuditStatus()))
                .ge(query.getStartTime() != null, Community::getCreateTime, query.getStartTime())
                .le(endTime != null, Community::getCreateTime, endTime)
                .ne(Community::getAudiStatus, AuditStatusEnum.RETURNED)
                .eq(Community::getIsEnable, "1")       // 逻辑删除
                .page(query.toMpPageDefaultSortByCreateTimeDesc());

        List<Community> records = page.getRecords();
        if (CollUtils.isEmpty(records)) {
            return PageDTO.empty(page);
        }

        // 如果有委员会、届期或次数参数，则需要查询委员信息
        List<String> committeeMembers = null;
        if (StringUtils.isNotEmpty(query.getCommittee()) || StringUtils.isNotEmpty(query.getElectedPeriod()) || StringUtils.isNotEmpty(query.getElectedTimes())) {
            // 创建查询DTO
            CommitteeMemberQueryDTO memberQueryDTO = new CommitteeMemberQueryDTO();
            memberQueryDTO.setBelongsSpecialCommittee(query.getCommittee());
            memberQueryDTO.setElectedPeriod(query.getElectedPeriod());
            memberQueryDTO.setElectedTimes(query.getElectedTimes());

            // 查询委员信息
            List<CommitteeMemberQueryVo> members = committeeMemberService.selectCommitteeMemberByCondition(memberQueryDTO);

            // 如果没有找到委员，返回空结果
            if (CollUtils.isEmpty(members)) {
                return PageDTO.<CommunityPageVo>empty(0L, 0L);
            }

            // 提取委员姓名
            committeeMembers = members.stream()
                    .map(CommitteeMemberQueryVo::getUserName)
                    .collect(Collectors.toList());

            // 根据委员姓名过滤公益情况记录
            final List<String> finalCommitteeMembers = committeeMembers;
            List<CommunityPageVo> filteredList = records.stream()
                    .filter(community -> {
                        String participantsName = community.getParticipantsName();
                        if (StringUtils.isEmpty(participantsName)) {
                            return false; // 如果参与者为空，跳过此记录
                        }
                        return java.util.Arrays.stream(participantsName.split(","))
                                .map(String::trim)
                                .anyMatch(finalCommitteeMembers::contains);
                    })
                    .map(this::convertToPageVo)
                    .collect(Collectors.toList());

            // 返回过滤后的结果
            return new PageDTO<CommunityPageVo>(Long.valueOf(filteredList.size()), Long.valueOf(Math.min(1, (filteredList.size() + query.getPageSize() - 1) / query.getPageSize())), filteredList);
        }

        // 如果没有届期、次数或委员会参数，返回所有记录
        List<CommunityPageVo> voList = records.stream()
                .map(this::convertToPageVo)
                .collect(Collectors.toList());

        return PageDTO.<CommunityPageVo>of(page, voList);
    }

    /**
     * 将Community实体转换为CommunityPageVo
     *
     * @param community 公益情况实体
     * @return 公益情况列表VO
     */
    private CommunityPageVo convertToPageVo(Community community) {
        CommunityPageVo vo = new CommunityPageVo();
        vo.setId(community.getPkid());
        BeanUtil.copyProperties(community, vo);

        if (community.getAudiStatus() != null) {
            vo.setAuditStatus(community.getAudiStatus().getLabel());
        }

        // 设置社区类型名称
        if (community.getCommunityType() != null) {
            vo.setCommunityTypeName(community.getCommunityType().getLabel());
        }

        return vo;
    }

    /**
     * 将CommunityAnnex实体转换为CommunityAnnexVo
     *
     * @param annex 附件实体
     * @return 附件VO
     */
    private CommunityAnnexVo convertToAnnexVo(CommunityAnnex annex) {
        CommunityAnnexVo annexVo = new CommunityAnnexVo();
        BeanUtil.copyProperties(annex, annexVo);
        return annexVo;
    }

    @Override
    public CommunityDetailVo getCommunityDetail(String id) {
        Community community = getById(id);
        if (community == null) {
            throw new RuntimeException("公益情况信息不存在");
        }

        // 创建详情VO并复制基本属性
        CommunityDetailVo detailVo = new CommunityDetailVo();
        BeanUtil.copyProperties(community, detailVo);
        detailVo.setId(community.getPkid());

        // 设置审核状态和类型名称
        if (community.getAudiStatus() != null) {
            detailVo.setAuditStatus(community.getAudiStatus().getCode());
            detailVo.setAuditStatusName(community.getAudiStatus().getLabel());
        }
        if (community.getCommunityType() != null) {
            detailVo.setCommunityType(community.getCommunityType().getCode());
            detailVo.setCommunityTypeName(community.getCommunityType().getLabel());
        }

        // 获取并转换附件列表
        List<CommunityAnnex> annexList = communityAnnexService.selectListByCommunityId(id);
        if (!CollUtils.isEmpty(annexList)) {
            List<CommunityAnnexVo> annexVoList = annexList.stream()
                    .map(this::convertToAnnexVo)
                    .collect(Collectors.toList());
            detailVo.setAnnexList(annexVoList);
        } else {
            detailVo.setAnnexList(Collections.emptyList());
        }

        List<ParticipantVo> participantList = examineParticipantService.getBaseMapper().selectParticipantByReportPkId(id);
        detailVo.setParticipantList(participantList);

        return detailVo;
    }

    /**
     * 处理附件
     *
     * @param annexDTOList 附件DTO列表
     * @param communityId 公益情况ID
     * @param isUpdate 是否是更新操作
     * @return 处理结果，成功返回null，失败返回错误信息
     */
    private AjaxResult processAnnexes(List<CommunityAnnexDTO> annexDTOList, String communityId, boolean isUpdate) {
        // 更新操作时，先删除原有附件
        if (isUpdate && annexDTOList != null) {
            communityAnnexService.deleteByCommunityId(communityId);
            if (CollUtils.isEmpty(annexDTOList)) {
                return null;
            }
        }

        if (CollUtils.isEmpty(annexDTOList)) {
            return null;
        }

        // 创建新附件实体并保存
        List<CommunityAnnex> annexList = annexDTOList.stream()
                .map(annexDTO -> {
                    CommunityAnnex annex = new CommunityAnnex();
                    BeanUtil.copyProperties(annexDTO, annex);
                    annex.setCommunityId(communityId);
                    annex.setCreateBy(SecurityUtils.getUsername());
                    annex.setCreateTime(new Date());
                    return annex;
                })
                .collect(Collectors.toList());

        return communityAnnexService.saveBatch(annexList) ? null : AjaxResult.error("保存附件信息失败");
    }

    /**
     * 生成公益情况ID
     *
     * @return 新的ID，格式为时间戳-随机数
     */
    private String generateCommunityId() {
        return System.currentTimeMillis() + "-" + String.format("%018d", (long) (Math.random() * 1000000000000000000L));
    }

    /**
     * 设置社区类型
     *
     * @param typeCode 类型编码
     * @return 社区类型枚举
     */
    private CommunityTypeEnum resolveCommunityType(String typeCode) {
        if (StringUtils.isEmpty(typeCode)) {
            return CommunityTypeEnum.DO_PUBLIC_WELFARE;
        }

        CommunityTypeEnum communityType = CommunityTypeEnum.fromValue(typeCode);
        return communityType != null ? communityType : CommunityTypeEnum.DO_PUBLIC_WELFARE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult addCommunity(CommunityAddDTO communityAddDTO) {
        if (communityAddDTO == null) {
            return AjaxResult.error("参数不能为空");
        }

        Community community = new Community();
        BeanUtil.copyProperties(communityAddDTO, community, CopyOptions.create().setIgnoreProperties("communityType"));


        String id = generateCommunityId();
        community.setPkid(id);
        community.setCommunityType(resolveCommunityType(communityAddDTO.getCommunityType()));
        community.setCreateUser(SecurityUtils.getUsername());
        community.setCreateTime(new Date());
        community.setAudiStatus(AuditStatusEnum.UNREVIEWED);
        community.setStatus("1");
        community.setIsEnable("1");

        if (!save(community)) {
            return AjaxResult.error("保存公益情况信息失败");
        }

        AjaxResult annexResult = processAnnexes(communityAddDTO.getAnnexList(), id, false);
        if (annexResult != null) {
            return annexResult;
        }
        if (ObjectUtil.isNotEmpty(communityAddDTO.getParticipantList())) {
            List<ExamineParticipant> participantList = buildExamineParticipants(id, communityAddDTO.getParticipantList());
            examineParticipantService.saveBatch(participantList);
        }

        return AjaxResult.success("新增公益情况信息成功", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateCommunity(CommunityUpdateDTO communityUpdateDTO) {
        // 参数验证
        if (communityUpdateDTO == null || StringUtils.isEmpty(communityUpdateDTO.getId())) {
            return AjaxResult.error("参数不能为空");
        }

        // 获取并验证实体
        String id = communityUpdateDTO.getId();
        Community community = getById(id);
        if (community == null) {
            return AjaxResult.error("公益情况信息不存在");
        }

        BeanUtil.copyProperties(communityUpdateDTO, community);

        community.setAudiStatus(AuditStatusEnum.UNREVIEWED);
        // 设置更新人和更新时间
        community.setUpdateUser(SecurityUtils.getUsername());
        community.setUpdateTime(new Date());

        // 保存实体
        if (!updateById(community)) {
            return AjaxResult.error("更新公益情况信息失败");
        }

        // 处理参与者
        if (ObjectUtil.isNotEmpty(communityUpdateDTO.getParticipantList())) {
            List<ExamineParticipant> participantList = buildExamineParticipants(id, communityUpdateDTO.getParticipantList());
            examineParticipantService.resetParticipant(id, participantList);
        }


        // 处理附件
        AjaxResult annexResult = processAnnexes(communityUpdateDTO.getAnnexList(), id, true);
        if (annexResult != null) {
            return annexResult;
        }

        return AjaxResult.success("更新公益情况信息成功");
    }


    /**
     * 验证并获取公益情况列表
     *
     * @param ids ID列表
     * @return 公益情况列表或错误结果
     */
    private AjaxResult validateAndGetCommunities(List<String> ids) {
        if (CollUtils.isEmpty(ids)) {
            return AjaxResult.error("参数不能为空");
        }

        List<Community> communityList = listByIds(ids);
        if (CollUtils.isEmpty(communityList)) {
            return AjaxResult.error("未找到相关公益情况信息");
        }

        return AjaxResult.success(communityList);
    }

    /**
     * 处理审核或退回操作
     *
     * @param communityAuditDTO 审核信息
     * @param targetStatus 目标状态
     * @param operationType 操作类型（审核或退回）
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult processAuditOperation(CommunityAuditDTO communityAuditDTO, AuditStatusEnum targetStatus, String operationType) {
        if (communityAuditDTO == null || CollUtils.isEmpty(communityAuditDTO.getIds())) {
            return AjaxResult.error("参数不能为空");
        }

        // 验证并获取公益情况列表
        AjaxResult validationResult = validateAndGetCommunities(communityAuditDTO.getIds());
        if (validationResult.isError()) {
            return validationResult;
        }

        @SuppressWarnings("unchecked")
        List<Community> communityList = (List<Community>) validationResult.get(AjaxResult.DATA_TAG);

        // 过滤出待审核的数据
        List<Community> pendingCommunities = communityList.stream()
                .filter(community -> community.getAudiStatus() == AuditStatusEnum.UNREVIEWED)
                .collect(Collectors.toList());

        if (CollUtils.isEmpty(pendingCommunities)) {
            return AjaxResult.error("没有可" + operationType + "的数据");
        }

        // 设置审核/退回信息
        final Date now = new Date();
        final Date checkTime = communityAuditDTO.getCheckTime() != null ? communityAuditDTO.getCheckTime() : now;
        final String username = SecurityUtils.getUsername();

        pendingCommunities.forEach(community -> {
            community.setAudiStatus(targetStatus);
            community.setChecker(communityAuditDTO.getChecker());
            community.setCheckerPhone(communityAuditDTO.getCheckerPhone());
            community.setCheckTime(checkTime);
            community.setCheckReason(communityAuditDTO.getCheckReason());
            community.setUpdateUser(username);
            community.setUpdateTime(now);
        });

        // 批量更新
        boolean updated = updateBatchById(pendingCommunities);
        if (!updated) {
            return AjaxResult.error(operationType + "失败");
        }

        return AjaxResult.success(operationType + "成功，共" + operationType + pendingCommunities.size() + "条数据");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult auditCommunity(CommunityAuditDTO communityAuditDTO) {
        return processAuditOperation(communityAuditDTO, AuditStatusEnum.REVIEWED, "审核");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult returnCommunity(CommunityAuditDTO communityAuditDTO) {
        return processAuditOperation(communityAuditDTO, AuditStatusEnum.RETURNED, "退回");
    }

    @Override
    public PageDTO<CommunityPageVo> getCurrentUserCommunityList(CommunityUserPageQuery query) {
        query.setPageNo(query.getCurrentPage());

        String currentUserId = String.valueOf(SecurityUtils.getUserId());
        // TODO

        Set<String> reportIdList = examineParticipantService.getBaseMapper().selectReportPkIdByCommittee(null, null, null, currentUserId, null);
        if (ObjectUtil.isEmpty(reportIdList)) {
            return PageDTO.empty(0L, 0L);
        }

        // 构建查询条件
        Page<Community> page = lambdaQuery()
                .like(StringUtils.isNotEmpty(query.getTitle()), Community::getTitle, query.getTitle())
                .eq(StringUtils.isNotEmpty(query.getAuditStatus()), Community::getAudiStatus, AuditStatusEnum.fromValue(query.getAuditStatus()))
                .eq(StringUtils.isNotEmpty(query.getStatus()), Community::getStatus, query.getStatus())
                .in(Community::getPkid, reportIdList)
                .eq(Community::getIsEnable, "1")
                .page(query.toMpPageDefaultSortByCreateTimeDesc());

        List<Community> records = page.getRecords();
        if (CollUtils.isEmpty(records)) {
            return PageDTO.empty(page);
        }

        // 转换为VO对象
        List<CommunityPageVo> voList = records.stream()
                .map(this::convertToPageVo)
                .collect(Collectors.toList());

        return PageDTO.of(page, voList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteCommunity(List<String> ids) {
        // 验证并获取公益情况列表
        AjaxResult validationResult = validateAndGetCommunities(ids);
        if (validationResult.isError()) {
            return validationResult;
        }

        @SuppressWarnings("unchecked")
        List<Community> communityList = (List<Community>) validationResult.get(AjaxResult.DATA_TAG);

        final Date now = new Date();
        final String username = SecurityUtils.getUsername();

        communityList.forEach(community -> {
            community.setIsEnable("0"); // 设置为已删除
            community.setUpdateUser(username);
            community.setUpdateTime(now);
        });

        // 批量更新
        boolean updated = updateBatchById(communityList);
        if (!updated) {
            return AjaxResult.error("删除失败");
        }

        return AjaxResult.success("删除成功，共删除" + communityList.size() + "条数据");
    }

    private List<ExamineParticipant> buildExamineParticipants(String communityId, List<String> participantList) {
        return participantList.stream()
                .map(participant -> {
                    ExamineParticipant examineParticipant = new ExamineParticipant();
                    examineParticipant.setReportPkid(communityId);
                    examineParticipant.setCommitteePkid(participant);
                    examineParticipant.setReportType(ReportTypeEnum.WELFARE);
                    examineParticipant.setPersonType(PersonTypeEnum.PARTICIPANT);
                    return examineParticipant;
                })
                .collect(Collectors.toList());
    }
}
