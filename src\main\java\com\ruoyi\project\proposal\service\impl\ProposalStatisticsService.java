package com.ruoyi.project.proposal.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.enums.proposal.CaseFillingEnum;
import com.ruoyi.common.enums.proposal.UndertakeResultEnum;
import com.ruoyi.common.enums.proposal.UndertakeWayEnum;
import com.ruoyi.common.utils.poi.WordUtil;
import com.ruoyi.project.proposal.domain.Proposal;
import com.ruoyi.project.proposal.domain.ProposalHandle;
import com.ruoyi.project.proposal.domain.dto.ProposalStatisticsDto;
import com.ruoyi.project.proposal.domain.vo.*;
import com.ruoyi.project.proposal.mapper.ProposalMapper;
import com.ruoyi.project.proposal.mapper.ProposalStatisticsMapper;
import com.ruoyi.project.proposal.service.IProposalService;
import com.ruoyi.project.system.domain.SysUser;
import com.ruoyi.project.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.springframework.stereotype.Service;


import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProposalStatisticsService {

    @Resource
    private ProposalStatisticsMapper proposalstatisticsMapper;

    @Resource
    private SysUserMapper userMapper;

    @Resource
    private ProposalMapper proposalMapper;


    private void wrapStatisticsDto(ProposalStatisticsDto statisticsDto) {
        if (ObjectUtil.isEmpty(statisticsDto.getStatisticalYear())) {
            statisticsDto.setStatisticalYear(DateUtil.thisYear());
        }

        if (ObjectUtil.isNotEmpty(statisticsDto.getNature())) {
            SysUser searchDto = new SysUser();
            searchDto.setDeptId(Long.valueOf(statisticsDto.getNature()));
            List<SysUser> sysUsers = userMapper.selectUserList(searchDto);
            Set<Long> userIdList = sysUsers.stream().map(SysUser::getUserId).collect(Collectors.toSet());
            statisticsDto.setUserIdList(userIdList);
        } else {
            statisticsDto.setUserIdList(null);
        }
    }

    /**
     * 获取办理结果统计数据
     *
     * @param statisticsDto 查询条件
     * @return 统计数据
     */
    public List<HandleStatisticsVo> analyzeConsultResult(ProposalStatisticsDto statisticsDto) {
        wrapStatisticsDto(statisticsDto);

        List<ProposalHandle> handleList = proposalstatisticsMapper.selectResultStatistics(statisticsDto.getStatisticalYear());

        // 获取处理结果的统计数据
        Map<String, Long> collect = handleList.stream()
                .collect(Collectors.groupingBy(item ->
                        Optional.ofNullable(item.getUndertakeResult()).orElse("E"),
                        TreeMap::new,
                        Collectors.counting()
                        )
                );

        for (UndertakeResultEnum value : UndertakeResultEnum.values()) {
            collect.putIfAbsent(value.name(), 0L);
        }

        List<HandleStatisticsVo> handleStatisticsList = new ArrayList<>();
        int index = 1;
        for (Map.Entry<String, Long> entry : collect.entrySet()) {
            HandleStatisticsVo handleStatisticsVo = new HandleStatisticsVo();
            handleStatisticsVo.setNo(index++);
            handleStatisticsVo.setLabel(UndertakeResultEnum.getSolutionDesc(entry.getKey()));
            handleStatisticsVo.setCount(entry.getValue());
            handleStatisticsList.add(handleStatisticsVo);
            ;
        }

        return handleStatisticsList;
    }


    /**
     * 单位件数统计
     * @param statisticsDto 统计参数
     * @return result
     */
    public List<ProposalUnitStatisticVo> analyzeDeptProposalCount(ProposalStatisticsDto statisticsDto) {
        wrapStatisticsDto(statisticsDto);

        List<ProposalUnitStatisticVo> statisticVoList = proposalstatisticsMapper.analyzeDeptProposalCount(statisticsDto);

        if (ObjectUtil.isEmpty(statisticVoList)) {
            return Collections.emptyList();
        }

        List<ProposalUnitStatisticVo> filteredList = statisticVoList.stream()
                .filter(vo -> ObjectUtil.isNotEmpty(vo.getUnitName()))
                .collect(Collectors.toList());


        Map<String, List<ProposalUnitStatisticVo>> groupedMap = filteredList.stream()
                .collect(Collectors.groupingBy(
                        ProposalUnitStatisticVo::getUnitName,
                        Collectors.collectingAndThen(
                                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ProposalUnitStatisticVo::getCaseNumber))),
                                ArrayList::new
                        )
                ));

        List<ProposalUnitStatisticVo> resultList = new ArrayList<>();
        for (List<ProposalUnitStatisticVo> unitData : groupedMap.values()) {
            resultList.addAll(unitData);
        }

        return resultList;
    }

    public void exportDeptProposal(ProposalStatisticsDto statisticsDto, HttpServletResponse response) {
        List<ProposalUnitStatisticVo> statisticList = this.analyzeDeptProposalCount(statisticsDto);

        try (XWPFDocument document = new XWPFDocument()) {
            XWPFParagraph title = document.createParagraph();
            title.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = title.createRun();
            titleRun.setText(statisticsDto.getStatisticalYear() + "年单位件数统计报表");
            titleRun.setFontFamily("微软雅黑");
            titleRun.setFontSize(22);


            XWPFParagraph count = document.createParagraph();
            count.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun countRun = count.createRun();
            countRun.setText("总条数：" + statisticList.size() + "条");
            countRun.setFontFamily("微软雅黑");
            countRun.setFontSize(10);
            countRun.setBold(true);

            XWPFTable table = document.createTable(statisticList.size() + 1, 4);

            // 设置表头
            setHeaderCell(table.getRow(0).getCell(0), "承办单位");
            setHeaderCell(table.getRow(0).getCell(1), "流水号");
            setHeaderCell(table.getRow(0).getCell(2), "案号");
            setHeaderCell(table.getRow(0).getCell(3), "案由");

            Map<String, List<Integer>> unitRowsMap = new HashMap<>();

            for (int i = 0; i < statisticList.size(); i++) {
                ProposalUnitStatisticVo vo = statisticList.get(i);
                int rowIndex = i + 1;

                // 填充数据
                table.getRow(rowIndex).getCell(0).setText(vo.getUnitName());
                table.getRow(rowIndex).getCell(1).setText(vo.getSerialNumber());
                table.getRow(rowIndex).getCell(2).setText(vo.getCaseNumber());
                table.getRow(rowIndex).getCell(3).setText(vo.getCaseReason());

                // 记录相同单位的行号
                String unitName = vo.getUnitName();
                if (!unitRowsMap.containsKey(unitName)) {
                    unitRowsMap.put(unitName, new ArrayList<>());
                }
                unitRowsMap.get(unitName).add(rowIndex);
            }

            // 合并相同单位的行
            for (Map.Entry<String, List<Integer>> entry : unitRowsMap.entrySet()) {
                List<Integer> rows = entry.getValue();
                if (rows.size() > 1) {
                    // 如果有多行相同单位，合并单位列
                    int firstRow = rows.get(0);
                    int lastRow = rows.get(rows.size() - 1);

                    // 合并单位列
                    mergeCellVertically(table, 0, firstRow, lastRow);
                }
            }

            String fileName = "单位件数统计.docx";
            WordUtil.write(fileName, document, response);

        } catch (Exception e) {
            log.info("单位件数统计数据导出失败！ >> {}", e.getMessage());
        }
    }


    /**
     * 获取办理件数统计数据
     * @param statisticsDto 查询条件
     * @return 统计数据
     */
    public List<HandleCaseCountVo> analyzeByGovOrPartyDept(ProposalStatisticsDto statisticsDto) {

        wrapStatisticsDto(statisticsDto);

        List<UndertakeUnitVo> unitVoList = proposalstatisticsMapper.analyzeByGovOrPartyDept(statisticsDto);

        List<HandleCaseCountVo> handleCaseCountList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(unitVoList)) {
            // 按部门名称和承办方式分组统计
            // 首先过滤掉单位名称或承办方式为null的数据
            Map<String, Map<UndertakeWayEnum, Long>> statistics = unitVoList.stream()
                    .filter(unit -> unit.getUnitName() != null && unit.getUndertakeWay() != null)
                    .collect(
                        Collectors.groupingBy(
                                UndertakeUnitVo::getUnitName,
                            Collectors.groupingBy(
                                    UndertakeUnitVo::getUndertakeWay,
                                Collectors.counting()
                            )
                        )
                    );

            // 遍历统计结果，生成每个部门的办理件数统计数据
            Integer index = 1;
            for (Map.Entry<String, Map<UndertakeWayEnum, Long>> entry : statistics.entrySet()) {
                HandleCaseCountVo handleCaseCountVo = new HandleCaseCountVo(entry.getKey());
                Map<UndertakeWayEnum, Long> value = entry.getValue();
                handleCaseCountVo.setNo(index);

                // 设置各类办理方式的数量
                handleCaseCountVo.setSingleOffice(value.getOrDefault(UndertakeWayEnum.SINGLE_OFFICE, 0L));
                handleCaseCountVo.setDistributedWork(value.getOrDefault(UndertakeWayEnum.DISTRIBUTED_WORK, 0L));
                handleCaseCountVo.setLeadOffice(value.getOrDefault(UndertakeWayEnum.LEAD_OFFICE, 0L));
                handleCaseCountVo.setAssistantOffice(value.getOrDefault(UndertakeWayEnum.ASSISTANT_OFFICE, 0L));

                // 计算总数，不包括“会办”类型
                handleCaseCountVo.setTotal(value.entrySet().stream()
                        .filter(e -> e.getKey() != UndertakeWayEnum.JOINT_HANDLING)
                        .mapToLong(Map.Entry::getValue)
                        .sum());

                index++;
                handleCaseCountList.add(handleCaseCountVo);
            }

            // 添加合计行
            HandleCaseCountVo total = new HandleCaseCountVo("合计");
            total.setSingleOffice(handleCaseCountList.stream().mapToLong(HandleCaseCountVo::getSingleOffice).sum());
            total.setDistributedWork(handleCaseCountList.stream().mapToLong(HandleCaseCountVo::getDistributedWork).sum());
            total.setLeadOffice(handleCaseCountList.stream().mapToLong(HandleCaseCountVo::getLeadOffice).sum());
            total.setAssistantOffice(handleCaseCountList.stream().mapToLong(HandleCaseCountVo::getAssistantOffice).sum());
            total.setTotal(handleCaseCountList.stream().mapToLong(HandleCaseCountVo::getTotal).sum());
            handleCaseCountList.add(total);
        } else {
            // 如果没有数据，添加一个空的总计行
            handleCaseCountList.add(new HandleCaseCountVo("总计"));
        }

        return handleCaseCountList;
    }

    /**
     * 获取办理进度统计数据
     * @param statisticsDto 查询条件
     * @return 统计数据
     */
    public List<HandleProgressStatsVo> analyzeHandleProgress(ProposalStatisticsDto statisticsDto) {
        wrapStatisticsDto(statisticsDto);

        List<HandleProgressStatsVo> progressStatsList = proposalstatisticsMapper.analyzeHandleProgress(statisticsDto);




        // 使用新的查询方法，正确区分已报/未报状态
        CompletableFuture<Map<String, List<ProposalUnitStatisticVo>>> jointReportedFuture = CompletableFuture.supplyAsync(() ->
                proposalstatisticsMapper.selectJointOfficeReported(statisticsDto.getStatisticalYear()).stream()
                        .collect(Collectors.groupingBy(ProposalUnitStatisticVo::getUnitName))
        );

        CompletableFuture<Map<String, List<ProposalUnitStatisticVo>>> jointUnReportedFuture = CompletableFuture.supplyAsync(() ->
                proposalstatisticsMapper.selectJointOfficeUnReported(statisticsDto.getStatisticalYear()).stream()
                        .collect(Collectors.groupingBy(ProposalUnitStatisticVo::getUnitName))
        );

        CompletableFuture<Map<String, List<ProposalUnitStatisticVo>>> leadReportedFuture = CompletableFuture.supplyAsync(() ->
                proposalstatisticsMapper.selectLeadOfficeReported(statisticsDto.getStatisticalYear()).stream()
                        .collect(Collectors.groupingBy(ProposalUnitStatisticVo::getUnitName))
        );

        CompletableFuture<Map<String, List<ProposalUnitStatisticVo>>> leadUnReportedFuture = CompletableFuture.supplyAsync(() ->
                proposalstatisticsMapper.selectLeadOfficeUnReported(statisticsDto.getStatisticalYear()).stream()
                        .collect(Collectors.groupingBy(ProposalUnitStatisticVo::getUnitName))
        );

        CompletableFuture.allOf(jointReportedFuture, jointUnReportedFuture, leadReportedFuture, leadUnReportedFuture).join();

        Map<String, List<ProposalUnitStatisticVo>> jointReportedMap = jointReportedFuture.join();
        Map<String, List<ProposalUnitStatisticVo>> jointUnReportedMap = jointUnReportedFuture.join();
        Map<String, List<ProposalUnitStatisticVo>> leadReportedMap = leadReportedFuture.join();
        Map<String, List<ProposalUnitStatisticVo>> leadUnReportedMap = leadUnReportedFuture.join();

        // 设置会办已报统计
        BiConsumer<HandleProgressStatsVo, List<ProposalUnitStatisticVo>> setJointReportedStats = (statsVo, list) -> {
            statsVo.setCoordinatorReportedCount(list.size());
            statsVo.setCoordinatorReportedStr(list.stream()
                    .map(ProposalUnitStatisticVo::getCaseNumber)
                    .collect(Collectors.joining(",")));
        };

        // 设置会办未报统计
        BiConsumer<HandleProgressStatsVo, List<ProposalUnitStatisticVo>> setJointUnReportedStats = (statsVo, list) -> {
            statsVo.setCoordinatorUnReportedCount(list.size());
            statsVo.setCoordinatorUnReportedStr(list.stream()
                    .map(ProposalUnitStatisticVo::getCaseNumber)
                    .collect(Collectors.joining(",")));
        };

        // 设置主办已报统计
        BiConsumer<HandleProgressStatsVo, List<ProposalUnitStatisticVo>> setLeadReportedStats = (statsVo, list) -> {
            statsVo.setHostReportCount(list.size());
            statsVo.setHostReportStr(list.stream()
                    .map(ProposalUnitStatisticVo::getCaseNumber)
                    .collect(Collectors.joining(",")));
        };

        // 设置主办未报统计
        BiConsumer<HandleProgressStatsVo, List<ProposalUnitStatisticVo>> setLeadUnReportedStats = (statsVo, list) -> {
            statsVo.setHostUnReportCount(list.size());
            statsVo.setHostUnReportStr(list.stream()
                    .map(ProposalUnitStatisticVo::getCaseNumber)
                    .collect(Collectors.joining(",")));
        };

        progressStatsList.forEach(statsVo -> {
            // 设置会办已报
            Optional.ofNullable(jointReportedMap.get(statsVo.getDeptName()))
                    .ifPresent(list -> setJointReportedStats.accept(statsVo, list));

            // 设置会办未报
            Optional.ofNullable(jointUnReportedMap.get(statsVo.getDeptName()))
                    .ifPresent(list -> setJointUnReportedStats.accept(statsVo, list));

            // 设置主办已报
            Optional.ofNullable(leadReportedMap.get(statsVo.getDeptName()))
                    .ifPresent(list -> setLeadReportedStats.accept(statsVo, list));

            // 设置主办未报
            Optional.ofNullable(leadUnReportedMap.get(statsVo.getDeptName()))
                    .ifPresent(list -> setLeadUnReportedStats.accept(statsVo, list));

            // 根据明细数据计算办理总数（主办已报 + 主办未报 + 会办已报 + 会办未报）
            long totalHandleCount =
                (long) statsVo.getHostReport().getCount() +
                (long) statsVo.getHostUnReport().getCount() +
                (long) statsVo.getCoordinatorReported().getCount() +
                (long) statsVo.getCoordinatorUnReported().getCount();
            statsVo.setHandleCount(totalHandleCount);

            // 计算并设置总已报数量（主办已报 + 会办已报）
            long totalReportedCount =
                (long) statsVo.getHostReport().getCount() +
                (long) statsVo.getCoordinatorReported().getCount();
            statsVo.setHandleReportCount(totalReportedCount);
        });

        return progressStatsList;
    }

    /**
     * 垂直合并单元格
     *
     * @param table 表格
     * @param col 列号
     * @param fromRow 起始行
     * @param toRow 结束行
     */
    private void mergeCellVertically(XWPFTable table, int col, int fromRow, int toRow) {
        for (int i = fromRow; i <= toRow; i++) {
            XWPFTableCell cell = table.getRow(i).getCell(col);
            if (i == fromRow) {
                // 第一个单元格设置垂直合并
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
            } else {
                // 后续单元格设置为继续合并
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
                cell.getCTTc().getPArray(0).removeR(0);
            }
        }
    }

    /**
     * 设置表头单元格样式和内容
     *
     * @param cell 单元格
     * @param text 文本内容
     */
    private void setHeaderCell(XWPFTableCell cell, String text) {
        // 清除现有段落
        if (!cell.getParagraphs().isEmpty()) {
            for (int i = cell.getParagraphs().size() - 1; i >= 0; i--) {
                cell.removeParagraph(i);
            }
        }

        // 创建新段落并设置居中对齐
        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        // 创建运行并设置文本和样式
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        run.setBold(true);
        run.setFontSize(11);

        // 设置单元格背景色
        cell.setColor("D9D9D9");

        // 设置垂直居中
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
    }


    public List<ProposalReceptionStatisticsVo> analyzeProposalReception(ProposalStatisticsDto statisticsDto) {
        wrapStatisticsDto(statisticsDto);
        List<ProposalReceptionStatisticsVo> statisticsList = proposalstatisticsMapper.analyzeProposalReception(statisticsDto);

        Map<String, Long> countMap = statisticsList.stream()
                .collect(Collectors.groupingBy(ProposalReceptionStatisticsVo::getUnitName, Collectors.counting()));
        statisticsList.forEach(statistics -> {
            Long count = countMap.get(statistics.getUnitName());
            statistics.setTotal(count);
            if (ObjectUtil.isNotEmpty(statistics.getUndertakeWay())) {
                statistics.setCaseReason(statistics.getUndertakeWay() + statistics.getCaseReason());
            } else {
                statistics.setCaseReason(statistics.getCaseReason());
            }

        });

        statisticsList.sort(Comparator.comparing(ProposalReceptionStatisticsVo::getUnitName));

        return statisticsList;
    }

    public ProposalHandleStatisticsVo analyzeHandleNotice(ProposalStatisticsDto statisticsDto) {
        wrapStatisticsDto(statisticsDto);
        ProposalHandleStatisticsVo result = new ProposalHandleStatisticsVo();
        LambdaQueryWrapper<Proposal> queryWrapper = new LambdaQueryWrapper<Proposal>().eq(Proposal::getYear, statisticsDto.getStatisticalYear());

        // 收到提案
        Long receivedProposals = proposalMapper.selectCount(queryWrapper);
        result.setReceivedProposals(receivedProposals);
        // 立案件数 - 统计"立案、并案（主提案）、待办理、办结"这几个状态的数据
        LambdaQueryWrapper<Proposal> filedQueryWrapper = new LambdaQueryWrapper<Proposal>()
                .eq(Proposal::getYear, statisticsDto.getStatisticalYear())
                .in(Proposal::getCaseFiling, CaseFillingEnum.PUT_ON, CaseFillingEnum.MERGED,
                    CaseFillingEnum.WAIT_HANDLE, CaseFillingEnum.FINISH);
        Long filedProposals = proposalMapper.selectCount(filedQueryWrapper);
        result.setFiledProposals(filedProposals);

        List<HandleStatisticsVo> processedList = proposalstatisticsMapper.analyzeHandleCount(statisticsDto);
        Map<String, Long> map = new HashMap<>();

        for (HandleStatisticsVo vo : processedList) {
            if (vo.getLabel() != null) {
                map.put(vo.getLabel(), vo.getCount());
            }
        }

        // 单独办理
        result.setIndividuallyProcessed(map.getOrDefault(UndertakeWayEnum.SINGLE_OFFICE.name(), 0L));
        // 分别办理 - 应统计承办方式为"会办"的提案
        result.setSeparatelyProcessed(map.getOrDefault(UndertakeWayEnum.JOINT_HANDLING.name(), 0L));

        //  已办结提案的承办单位
        List<String> completedUnits = proposalstatisticsMapper.selectFinishProposalUnit(statisticsDto);
        result.setCompletedUnits(completedUnits);

        // 正在办理的承办单位
        List<String> processingUnits = proposalstatisticsMapper.selectProcessingProposalUnit(statisticsDto);
        result.setProcessingUnits(processingUnits);

        // 未开始办理的单位
        List<String> notStartedUnitNames = proposalstatisticsMapper.selectNotStartedProposalUnit(statisticsDto);
        result.setNotStartedUnitNames(notStartedUnitNames);

        // 解决程度
        StringBuilder solutionDegree = new StringBuilder();
        List<ProposalHandle> handleList = proposalstatisticsMapper.selectProposalHandle(statisticsDto);
        Map<String, Long> collect = handleList.stream()
                .collect(Collectors.groupingBy(ProposalHandle::getUndertakeResult,
                                TreeMap::new,
                                Collectors.counting()
                        )
                );

        for (UndertakeResultEnum value : UndertakeResultEnum.values()) {
            collect.putIfAbsent(value.name(), 0L);
        }
        Long total = collect.entrySet().stream().filter(entry -> !entry.getKey().equals(UndertakeResultEnum.E.name()))
                .mapToLong(Map.Entry::getValue)
                .sum();

        NumberFormat percentFormat = DecimalFormat.getPercentInstance();
        percentFormat.setMinimumFractionDigits(2);
        percentFormat.setMaximumFractionDigits(2);

        if (total > 0) {
            for (UndertakeResultEnum value : UndertakeResultEnum.values()) {
                if (!value.name().equals(UndertakeResultEnum.E.name())) {
                    String substring = value.getDescription().substring(value.getDescription().indexOf(":") + 1);
                    solutionDegree.append("解决程度").append(value.name());
                    solutionDegree.append("(").append(substring).append(")的占").append(collect.get(value.name())).append("件次，占提案答复总件次的")
                            .append(percentFormat.format(collect.get(value.name()) / (double)total)).append(";");
                }
            }
        } else {
            solutionDegree.append("暂无解决程度统计数据");
        }
        result.setSolutionDegree(solutionDegree.toString());
        return result;
    }

    /**
     * 委员提案统计
     * @param year 年份
     * @return 统计结果
     */
    public List<MemberProposalStatisticsVo> analyzeMemberProposal(Integer year) {
        return proposalstatisticsMapper.analyzeMemberProposal(year);
    }

    /**
     * 委员提案统计导出
     * @param year 年份
     * @param response response
     */
    public void exportMemberProposal(Integer year, HttpServletResponse response) {
        List<MemberProposalStatisticsVo> statisticsList = this.analyzeMemberProposal(year);

        try (XWPFDocument document = new XWPFDocument()) {
            // 创建标题
            XWPFParagraph title = document.createParagraph();
            title.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = title.createRun();
            titleRun.setText(year + "年度委员提案数量统计");
            titleRun.setFontFamily("微软雅黑");
            titleRun.setFontSize(18);
            titleRun.setBold(true);

            // 添加总条数显示
            XWPFParagraph countParagraph = document.createParagraph();
            countParagraph.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun countRun = countParagraph.createRun();
            countRun.setText("总条数：" + statisticsList.size() + "条");
            countRun.setFontFamily("微软雅黑");
            countRun.setFontSize(10);

            // 创建表格 (数据行数 + 表头行)，6列包含序号
            XWPFTable table = document.createTable(statisticsList.size() + 1, 6);

            // 设置表格宽度和列宽
            table.setWidth("100%");

            // 设置列宽比例：序号(8%) 代表姓名(20%) 单独或领衔(18%) 附议(18%) 立案(18%) 重要建议(18%)
            int[] colWidths = {800, 2000, 1800, 1800, 1800, 1800}; // 单位：twips (1/20 point)
            for (int i = 0; i < 6; i++) {
                table.getRow(0).getCell(i).setWidth(String.valueOf(colWidths[i]));
            }

            // 设置表头
            setMemberStatisticsHeaderCell(table.getRow(0).getCell(0), "序号");
            setMemberStatisticsHeaderCell(table.getRow(0).getCell(1), "代表姓名");
            setMemberStatisticsHeaderCell(table.getRow(0).getCell(2), "单独或领衔");
            setMemberStatisticsHeaderCell(table.getRow(0).getCell(3), "附议");
            setMemberStatisticsHeaderCell(table.getRow(0).getCell(4), "立案");
            setMemberStatisticsHeaderCell(table.getRow(0).getCell(5), "重要建议");

            // 填充数据
            for (int i = 0; i < statisticsList.size(); i++) {
                MemberProposalStatisticsVo vo = statisticsList.get(i);
                int rowIndex = i + 1;

                XWPFTableRow row = table.getRow(rowIndex);
                // 设置行高
                row.setHeight(600); // 设置行高为600 twips (约30pt)

                // 设置每列的宽度
                for (int j = 0; j < 6; j++) {
                    row.getCell(j).setWidth(String.valueOf(colWidths[j]));
                }

                // 设置数据单元格
                setMemberStatisticsDataCell(row.getCell(0), String.valueOf(rowIndex));
                setMemberStatisticsDataCell(row.getCell(1), vo.getMemberName());
                setMemberStatisticsDataCell(row.getCell(2), String.valueOf(vo.getIndividualCount()));
                setMemberStatisticsDataCell(row.getCell(3), String.valueOf(vo.getMotionCount()));
                setMemberStatisticsDataCell(row.getCell(4), "0"); // 立案暂时为0
                setMemberStatisticsDataCell(row.getCell(5), String.valueOf(vo.getImportantCount()));
            }

            String fileName = year + "年度委员提案数量统计.docx";
            WordUtil.write(fileName, document, response);

        } catch (Exception e) {
            log.info("委员提案统计数据导出失败！ >> {}", e.getMessage());
        }
    }

    /**
     * 设置委员统计表头单元格样式和内容
     *
     * @param cell 单元格
     * @param text 文本内容
     */
    private void setMemberStatisticsHeaderCell(XWPFTableCell cell, String text) {
        // 清除现有段落
        if (!cell.getParagraphs().isEmpty()) {
            for (int i = cell.getParagraphs().size() - 1; i >= 0; i--) {
                cell.removeParagraph(i);
            }
        }

        // 创建新段落并设置居中对齐
        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        // 设置段落间距
        paragraph.setSpacingBefore(100); // 段前间距
        paragraph.setSpacingAfter(100);  // 段后间距

        // 创建运行并设置文本和样式
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        run.setBold(true);
        run.setFontSize(10); // 5号字体对应10pt
        run.setFontFamily("微软雅黑");

        // 设置单元格背景色
        cell.setColor("D9D9D9");

        // 设置垂直居中
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
    }

    /**
     * 设置委员统计数据单元格样式和内容
     *
     * @param cell 单元格
     * @param text 文本内容
     */
    private void setMemberStatisticsDataCell(XWPFTableCell cell, String text) {
        // 清除现有段落
        if (!cell.getParagraphs().isEmpty()) {
            for (int i = cell.getParagraphs().size() - 1; i >= 0; i--) {
                cell.removeParagraph(i);
            }
        }

        // 创建新段落并设置居中对齐
        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        // 设置段落间距
        paragraph.setSpacingBefore(100); // 段前间距
        paragraph.setSpacingAfter(100);  // 段后间距

        // 创建运行并设置文本和样式
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        run.setFontSize(10); // 5号字体对应10pt
        run.setFontFamily("微软雅黑");

        // 设置垂直居中
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
    }

    /**
     * 委员提案详细统计（立案/不立案分类）
     * @param year 年份
     * @return 统计结果
     */
    public List<MemberProposalDetailStatisticsVo> analyzeMemberProposalDetail(Integer year) {
        List<MemberProposalDetailStatisticsVo> result = proposalstatisticsMapper.analyzeMemberProposalDetail(year);

        // 计算每个委员的小计和总计
        result.forEach(MemberProposalDetailStatisticsVo::calculateTotals);

        return result;
    }

    /**
     * 委员提案详细统计导出
     * @param year 年份
     * @param response response
     */
    public void exportMemberProposalDetail(Integer year, HttpServletResponse response) {
        List<MemberProposalDetailStatisticsVo> statisticsList = this.analyzeMemberProposalDetail(year);

        try (XWPFDocument document = new XWPFDocument()) {
            // 创建标题
            XWPFParagraph title = document.createParagraph();
            title.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = title.createRun();
            titleRun.setText(year + "年度委员立案数量统计");
            titleRun.setFontFamily("微软雅黑");
            titleRun.setFontSize(18);
            titleRun.setBold(true);

            // 添加总条数显示
            XWPFParagraph countParagraph = document.createParagraph();
            countParagraph.setAlignment(ParagraphAlignment.RIGHT);
            XWPFRun countRun = countParagraph.createRun();
            countRun.setText("总条数：" + statisticsList.size() + "条");
            countRun.setFontFamily("微软雅黑");
            countRun.setFontSize(10);

            // 创建表格 (数据行数 + 表头2行)，10列
            XWPFTable table = document.createTable(statisticsList.size() + 2, 10);

            // 设置表格宽度和列宽
            table.setWidth("100%");

            // 设置列宽比例：序号(6%) 委员姓名(12%) 立案各列(9%*4) 不立案各列(9%*4) 总计(10%)
            int[] colWidths = {600, 1200, 900, 900, 900, 900, 900, 900, 900, 1000};

            // 设置表头 - 第一行
            setDetailStatisticsHeaderCell(table.getRow(0).getCell(0), "序号");
            setDetailStatisticsHeaderCell(table.getRow(0).getCell(1), "委员姓名");
            setDetailStatisticsHeaderCell(table.getRow(0).getCell(2), "立案");
            setDetailStatisticsHeaderCell(table.getRow(0).getCell(3), "");
            setDetailStatisticsHeaderCell(table.getRow(0).getCell(4), "");
            setDetailStatisticsHeaderCell(table.getRow(0).getCell(5), "");
            setDetailStatisticsHeaderCell(table.getRow(0).getCell(6), "不立案");
            setDetailStatisticsHeaderCell(table.getRow(0).getCell(7), "");
            setDetailStatisticsHeaderCell(table.getRow(0).getCell(8), "");
            setDetailStatisticsHeaderCell(table.getRow(0).getCell(9), "总计");

            // 设置表头 - 第二行
            setDetailStatisticsHeaderCell(table.getRow(1).getCell(0), "");
            setDetailStatisticsHeaderCell(table.getRow(1).getCell(1), "");
            setDetailStatisticsHeaderCell(table.getRow(1).getCell(2), "单独");
            setDetailStatisticsHeaderCell(table.getRow(1).getCell(3), "领衔");
            setDetailStatisticsHeaderCell(table.getRow(1).getCell(4), "附议");
            setDetailStatisticsHeaderCell(table.getRow(1).getCell(5), "小计");
            setDetailStatisticsHeaderCell(table.getRow(1).getCell(6), "单独");
            setDetailStatisticsHeaderCell(table.getRow(1).getCell(7), "领衔");
            setDetailStatisticsHeaderCell(table.getRow(1).getCell(8), "附议");
            setDetailStatisticsHeaderCell(table.getRow(1).getCell(9), "小计");

            // 合并表头单元格
            // 垂直合并：序号、委员姓名、总计列
            mergeCellVertically(table, 0, 0, 1); // 序号列
            mergeCellVertically(table, 1, 0, 1); // 委员姓名列
            mergeCellVertically(table, 9, 0, 1); // 总计列

            // 水平合并：立案列(2-5)和不立案列(6-8)
            mergeCellHorizontally(table, 0, 2, 5); // 第一行"立案"合并4列
            mergeCellHorizontally(table, 0, 6, 8); // 第一行"不立案"合并3列

            // 填充数据
            for (int i = 0; i < statisticsList.size(); i++) {
                MemberProposalDetailStatisticsVo vo = statisticsList.get(i);
                int rowIndex = i + 2; // 从第3行开始（0,1是表头）

                XWPFTableRow row = table.getRow(rowIndex);
                // 设置行高
                row.setHeight(600);

                // 设置每列的宽度
                for (int j = 0; j < 10; j++) {
                    row.getCell(j).setWidth(String.valueOf(colWidths[j]));
                }

                // 设置数据单元格
                setDetailStatisticsDataCell(row.getCell(0), String.valueOf(i + 1));
                setDetailStatisticsDataCell(row.getCell(1), vo.getMemberName());
                setDetailStatisticsDataCell(row.getCell(2), String.valueOf(vo.getFiledIndividualCount()));
                setDetailStatisticsDataCell(row.getCell(3), String.valueOf(vo.getFiledLeaderCount()));
                setDetailStatisticsDataCell(row.getCell(4), String.valueOf(vo.getFiledMotionCount()));
                setDetailStatisticsDataCell(row.getCell(5), String.valueOf(vo.getFiledSubtotal()));
                setDetailStatisticsDataCell(row.getCell(6), String.valueOf(vo.getNotFiledIndividualCount()));
                setDetailStatisticsDataCell(row.getCell(7), String.valueOf(vo.getNotFiledLeaderCount()));
                setDetailStatisticsDataCell(row.getCell(8), String.valueOf(vo.getNotFiledMotionCount()));
                setDetailStatisticsDataCell(row.getCell(9), String.valueOf(vo.getTotal()));
            }

            String fileName = year + "年度委员立案数量统计.docx";
            WordUtil.write(fileName, document, response);

        } catch (Exception e) {
            log.info("委员提案详细统计数据导出失败！ >> {}", e.getMessage());
        }
    }

    /**
     * 设置详细统计表头单元格样式和内容
     *
     * @param cell 单元格
     * @param text 文本内容
     */
    private void setDetailStatisticsHeaderCell(XWPFTableCell cell, String text) {
        // 清除现有段落
        if (!cell.getParagraphs().isEmpty()) {
            for (int i = cell.getParagraphs().size() - 1; i >= 0; i--) {
                cell.removeParagraph(i);
            }
        }

        // 创建新段落并设置居中对齐
        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        // 设置段落间距
        paragraph.setSpacingBefore(100);
        paragraph.setSpacingAfter(100);

        // 创建运行并设置文本和样式
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        run.setBold(true);
        run.setFontSize(10); // 5号字体对应10pt
        run.setFontFamily("微软雅黑");

        // 设置单元格背景色
        cell.setColor("D9D9D9");

        // 设置垂直居中
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
    }

    /**
     * 设置详细统计数据单元格样式和内容
     *
     * @param cell 单元格
     * @param text 文本内容
     */
    private void setDetailStatisticsDataCell(XWPFTableCell cell, String text) {
        // 清除现有段落
        if (!cell.getParagraphs().isEmpty()) {
            for (int i = cell.getParagraphs().size() - 1; i >= 0; i--) {
                cell.removeParagraph(i);
            }
        }

        // 创建新段落并设置居中对齐
        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        // 设置段落间距
        paragraph.setSpacingBefore(100);
        paragraph.setSpacingAfter(100);

        // 创建运行并设置文本和样式
        XWPFRun run = paragraph.createRun();
        run.setText(text);
        run.setFontSize(10); // 5号字体对应10pt
        run.setFontFamily("微软雅黑");

        // 设置垂直居中
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
    }

    /**
     * 水平合并单元格
     *
     * @param table 表格
     * @param row 行号
     * @param fromCol 起始列
     * @param toCol 结束列
     */
    private void mergeCellHorizontally(XWPFTable table, int row, int fromCol, int toCol) {
        for (int i = fromCol; i <= toCol; i++) {
            XWPFTableCell cell = table.getRow(row).getCell(i);
            if (i == fromCol) {
                // 第一个单元格设置水平合并
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
            } else {
                // 后续单元格设置为继续合并
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
                // 清空后续单元格的内容
                if (!cell.getParagraphs().isEmpty()) {
                    for (int j = cell.getParagraphs().size() - 1; j >= 0; j--) {
                        cell.removeParagraph(j);
                    }
                }
                cell.addParagraph();
            }
        }
    }
}
