package com.ruoyi.project.committee.evalrule.strategy;

import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.evalrule.domain.vo.RuleDetailVo;
import com.ruoyi.project.committee.evalrule.mapper.BasicStrategyMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class BasicStrategy {

    private final BasicStrategyMapper basicStrategyMapper;
    
    /**
     * 一年内不提交提案
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void hasNotSubmittedProposalWithinOneYear(CommitteeMember member, RuleDetailVo ruleDetail) {
        if (basicStrategyMapper.hasNotSubmittedProposalWithinOneYear(member)) {
            ruleDetail.setFinalScore(Integer.valueOf(ruleDetail.getScore()));
        } else {
            ruleDetail.setFinalScore(0);
        }
    }

    /**
     * 获立案的个人提案（第一提案人）
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void countApprovedProposalsAsPrimaryProposer(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.countApprovedProposalsAsPrimaryProposer(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }

    /**
     * 获立案的个人提案（附议人）
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void countApprovedProposalsAsSecondaryProposer(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.countApprovedProposalsAsSecondaryProposer(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }

    /**
     * 1年内不提交社情民意（按年度累计计分）
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void hasNotSubmittedManuscriptWithinOneYear(CommitteeMember member, RuleDetailVo ruleDetail) {
        if (basicStrategyMapper.hasNotSubmittedManuscriptWithinOneYear(member)) {
            ruleDetail.setFinalScore(Integer.valueOf(ruleDetail.getScore()));
        } else {
            ruleDetail.setFinalScore(0);
        }
    }

    /**
     * 社情民意被采纳
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void countAcceptedManuscripts(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.countAcceptedManuscripts(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }

    /**
     * 获得区领导批示
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void countDistrictEndorsedManuscript(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.countDistrictEndorsedManuscript(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }

    /**
     * 获得市领导批示
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void countCityEndorsedManuscript(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.countCityEndorsedManuscript(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }


    /**
     * 获得省领导批示
     * @param member        member
     * @param ruleDetail    ruleDetail
     */
    public void countProvinceEndorsedManuscript(CommitteeMember member, RuleDetailVo ruleDetail) {
        Integer count = basicStrategyMapper.countProvinceEndorsedManuscript(member);
        ruleDetail.setFinalScore(count * Integer.parseInt(ruleDetail.getScore()));
    }
}
