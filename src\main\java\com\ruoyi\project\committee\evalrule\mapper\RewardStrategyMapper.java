package com.ruoyi.project.committee.evalrule.mapper;

import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface RewardStrategyMapper {

    /**
     * 参与"六个严禁两个推进"及水环境治理等生态环保民主监督活动（特别突出）
     */
    Integer handleEnvProtectionSupervisionOutstanding(CommitteeMember member);

    /**
     * 参与"六个严禁两个推进"及水环境治理等生态环保民主监督活动（表现较好）
     */
    Integer handleEnvProtectionSupervisionGood(CommitteeMember member);

    /**
     * 参与"六个严禁两个推进"及水环境治理等生态环保民主监督活动（有所参与）
     */
    Integer handleEnvProtectionSupervisionParticipated(CommitteeMember member);

    /**
     * 参加脱贫攻坚与乡村振兴工作（特别突出）
     */
    Integer handleRuralRevitalizationOutstanding(CommitteeMember member);

    /**
     * 参加脱贫攻坚与乡村振兴工作（表现较好）
     */
    Integer handleRuralRevitalizationGood(CommitteeMember member);

    /**
     * 参加脱贫攻坚与乡村振兴工作（有所参与）
     */
    Integer handleRuralRevitalizationParticipated(CommitteeMember member);

    /**
     * 参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（特别突出）
     */
    Integer handleGrassrootsDemocracyOutstanding(CommitteeMember member);

    /**
     * 参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（表现较好）
     */
    Integer handleGrassrootsDemocracyGood(CommitteeMember member);

    /**
     * 参加基层协商民主建设，积极下沉履职，搭建议事平台，引导参与协商，推动成果转化，参与基层社会治理（有所参与）
     */
    Integer handleGrassrootsDemocracyParticipated(CommitteeMember member);

    /**
     * 参加书香政协学习、宣讲活动（特别突出）
     */
    Integer handleReadingActivitiesOutstanding(CommitteeMember member);

    /**
     * 参加书香政协学习、宣讲活动（表现较好）
     */
    Integer handleReadingActivitiesGood(CommitteeMember member);

    /**
     * 参加书香政协学习、宣讲活动（有所参与）
     */
    Integer handleReadingActivitiesParticipated(CommitteeMember member);

    /**
     * 参加新冠肺炎疫情防控（特别突出）
     */
    Integer handleEpidemicPreventionOutstanding(CommitteeMember member);

    /**
     * 参加新冠肺炎疫情防控（表现较好）
     */
    Integer handleEpidemicPreventionGood(CommitteeMember member);

    /**
     * 参加新冠肺炎疫情防控（有所参与）
     */
    Integer handleEpidemicPreventionParticipated(CommitteeMember member);

    /**
     * 助力项目建设服务，参加引进外资活动（特别突出）
     */
    Integer handleProjectServiceOutstanding(CommitteeMember member);

    /**
     * 助力项目建设服务，参加引进外资活动（表现较好）
     */
    Integer handleProjectServiceGood(CommitteeMember member);

    /**
     * 助力项目建设服务，参加引进外资活动（有所参与）
     */
    Integer handleProjectServiceParticipated(CommitteeMember member);

    /**
     * 助推创一流营商环境（特别突出）
     */
    Integer handleBusinessEnvironmentOutstanding(CommitteeMember member);

    /**
     * 助推创一流营商环境（表现较好）
     */
    Integer handleBusinessEnvironmentGood(CommitteeMember member);

    /**
     * 助推创一流营商环境（有所参与）
     */
    Integer handleBusinessEnvironmentParticipated(CommitteeMember member);

    /**
     * 完成区政协交办的其他任务（特别突出）
     */
    Integer handleOtherTasksOutstanding(CommitteeMember member);

    /**
     * 完成区政协交办的其他任务（表现较好）
     */
    Integer handleOtherTasksGood(CommitteeMember member);

    /**
     * 完成区政协交办的其他任务（有所参与）
     */
    Integer handleOtherTasksParticipated(CommitteeMember member);
    
}
