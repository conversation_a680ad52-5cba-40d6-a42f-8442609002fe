# 分数为0和affectRows问题诊断修复

## 🚨 问题现象分析

### **从日志发现的问题**
```
11:33:49.784 [http-nio-8080-exec-2] INFO  - 计算完成，有分数的委员数量: 57/192
11:33:49.785 [http-nio-8080-exec-2] INFO  - 委员详情: ID=1902273680180912481, 姓名=黄旭, 基础分=0, 奖励分=0, 总分=0
11:33:49.884 [http-nio-8080-exec-2] DEBUG - 批次 1/4 完成，影响行数: 100
11:33:50.055 [http-nio-8080-exec-2] INFO  - 批量保存完成，总影响行数: 384
```

### **问题1: 大部分委员分数为0**
- **现象**: 只有57/192个委员有分数，其他135个委员都是0分
- **根因**: 策略计算没有正常执行，需要详细调试策略执行过程

### **问题2: affectRows = 384 (仍然是2倍)**
- **现象**: 批次影响行数 100+100+100+84=384，而不是预期的50+50+50+42=192
- **根因**: MySQL的`ON DUPLICATE KEY UPDATE`影响行数计算规则
  - INSERT操作：影响行数 = 1
  - UPDATE操作：影响行数 = 2

## ✅ 修复措施

### **修复1: 增强分数计算调试**
```java
// 只处理前3个委员进行详细调试
List<CommitteeMember> debugMembers = memberList.stream().limit(3).collect(Collectors.toList());

for (CommitteeMember member : debugMembers) {
    log.info("开始计算委员: {}", member.getUserName());
    // 详细的规则处理日志
    for (RuleDetailVo ruleDetailVo : ruleInfo.getChildren()) {
        log.info("处理顶级规则: {}", ruleDetailVo.getRemark());
        // 详细的策略执行日志
    }
}
```

**目的**: 
- 详细跟踪前3个委员的分数计算过程
- 识别策略执行失败的具体原因
- 其他委员暂时设为0分，避免大量日志

### **修复2: 提升关键日志级别**
```java
// 将关键的debug日志改为info级别
log.info("评估规则节点: {}, 策略键: {}, 是否有子节点: {}", ...);
log.info("叶子节点策略键为空，跳过: {}", ruleDetail.getRemark());
log.info("执行策略: 委员={}, 规则={}, 策略键={}, 策略类型={}, 方法={}", ...);
log.info("策略执行完成: 委员={}, 规则={}, 分数={}", ...);
```

**目的**: 确保能看到策略执行的详细过程

### **修复3: 修正affectRows计算**
```java
// 处理MySQL ON DUPLICATE KEY UPDATE的影响行数
int affected = ruleScoreMapper.upsertRuleScoresBatch(batch);
int processedRecords = (affected > batch.size()) ? batch.size() : affected;
totalAffected += processedRecords;

log.debug("批次 {}/{} 完成，MySQL影响行数: {}, 实际处理记录数: {}", 
         i + 1, batches.size(), affected, processedRecords);
```

**逻辑**:
- 如果 `affected > batch.size()`，说明有UPDATE操作，实际处理记录数 = `batch.size()`
- 如果 `affected <= batch.size()`，说明都是INSERT操作，实际处理记录数 = `affected`

## 🔍 预期调试信息

修复后应该能看到以下详细日志：

### **1. 委员计算过程**
```
开始计算委员: 黄旭
处理顶级规则: 基础分
评估规则节点: 基础分, 策略键: null, 是否有子节点: true
评估规则节点: [子规则1], 策略键: proposal:accepted:primary, 是否有子节点: false
执行策略: 委员=黄旭, 规则=[子规则1], 策略键=proposal:accepted:primary, 策略类型=BASIC, 方法=countApprovedProposalsAsPrimaryProposer
```

### **2. 策略执行结果**
```
策略执行完成: 委员=黄旭, 规则=[子规则1], 分数=10
顶级规则 基础分 最终分数: 25
累加基础分: 25
委员 黄旭 计算完成: 基础分=25, 奖励分=5, 总分=30
```

### **3. 批处理结果**
```
批次 1/4 完成，MySQL影响行数: 100, 实际处理记录数: 50
批次 2/4 完成，MySQL影响行数: 100, 实际处理记录数: 50
批次 3/4 完成，MySQL影响行数: 100, 实际处理记录数: 50
批次 4/4 完成，MySQL影响行数: 84, 实际处理记录数: 42
批量保存完成，总影响行数: 192
```

## 🎯 问题诊断清单

### **如果分数仍为0，检查：**
- [ ] 是否有"叶子节点策略键为空"的日志
- [ ] 是否有"未找到策略映射"的警告
- [ ] 是否有策略执行异常的错误日志
- [ ] BasicStrategyMapper的数据库查询日志是否出现

### **如果affectRows仍异常，检查：**
- [ ] 每个批次的"MySQL影响行数"和"实际处理记录数"
- [ ] 是否有批处理失败导致的单条重试
- [ ] 数据库中是否存在重复记录

## 📊 可能的问题原因

### **分数为0的可能原因**
1. **策略键为空** - 规则配置中strategyKey字段为空
2. **策略映射缺失** - 策略键在策略映射中不存在
3. **策略方法执行失败** - 反射调用或业务逻辑异常
4. **数据库查询无结果** - BasicStrategyMapper查询返回空结果

### **affectRows异常的原因**
1. **ON DUPLICATE KEY UPDATE机制** - MySQL的影响行数计算规则
2. **数据重复** - 同一委员的数据被多次处理
3. **批处理重试** - 失败重试导致重复计数

## 🔧 下一步行动

### **1. 立即测试**
运行修复后的代码，重点关注：
- 前3个委员的详细计算日志
- 策略执行是否正常
- affectRows是否修正为192

### **2. 根据日志结果调整**
- 如果策略键为空：检查规则配置
- 如果策略映射缺失：检查策略表数据
- 如果策略执行失败：检查具体异常信息

### **3. 逐步扩大范围**
- 确认前3个委员计算正确后
- 逐步增加调试委员数量
- 最终处理所有192个委员

## 总结

通过增强调试日志和修正affectRows计算，应该能够：
1. **快速定位分数为0的根本原因**
2. **正确显示实际处理的记录数**
3. **为后续全面修复提供准确的诊断信息**

关键是先解决分数计算问题，再优化性能和批处理逻辑。
