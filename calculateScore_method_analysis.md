# calculateScore 方法完整梳理

## 📋 方法概览

`calculateScore` 方法是规则分数计算的核心入口，负责计算指定年度所有委员的规则分数。

## 🔄 完整执行流程

### **1. 主入口方法 - calculateScore(Integer year)**

```java
@Transactional(rollbackFor = Exception.class)
public Integer calculateScore(Integer year)
```

#### **执行步骤：**
1. **数据准备阶段**
   ```java
   // 获取委员列表
   List<CommitteeMember> memberList = committeeMemberMapper.selectMemberByYear(year);
   
   // 获取规则信息
   RuleInfoVo ruleInfo = ruleInfoService.selectRuleInfoByYear(year);
   
   // 获取策略映射
   TripleMap<String, String, String> strategyMap = ruleStrategyService.getStrategyMap();
   ```

2. **分数计算阶段**
   ```java
   StopWatch stopWatch = new StopWatch();
   stopWatch.start("计算规则分数");
   
   List<RuleScore> ruleScoreList = calculateScore(memberList, ruleInfo, strategyMap);
   
   stopWatch.stop();
   ```

3. **数据保存阶段**
   ```java
   stopWatch.start("保存规则分数");
   Integer affectRows = upsertRuleScoresBatch(ruleScoreList);
   stopWatch.stop();
   ```

4. **统计和日志**
   ```java
   long nonZeroScores = ruleScoreList.stream()
       .mapToInt(RuleScore::getTotalScore)
       .filter(score -> score > 0)
       .count();
   log.info("计算完成，有分数的委员数量: {}/{}", nonZeroScores, ruleScoreList.size());
   ```

### **2. 核心计算方法 - calculateScore(memberList, ruleInfo, strategyMap)**

```java
private List<RuleScore> calculateScore(List<CommitteeMember> memberList, 
                                      RuleInfoVo ruleInfo,
                                      TripleMap<String, String, String> strategyMap)
```

#### **执行逻辑：**
```
FOR 每个委员 member IN memberList:
    初始化 basicScore = 0, rewardScore = 0
    
    FOR 每个顶级规则 ruleDetailVo IN ruleInfo.getChildren():
        1. resetFinalScores(ruleDetailVo)           // 重置规则树分数
        2. evaluateLeafNodes(member, ruleDetailVo)   // 计算叶子节点分数
        3. accumulateScoresByStrategyType()          // 按策略类型累加分数
        
        basicScore += accumulator.getBasicScore()
        rewardScore += accumulator.getRewardScore()
    
    构建 RuleScore 对象
    添加到 ruleScoreList
```

### **3. 叶子节点评估 - evaluateLeafNodes()**

```java
private void evaluateLeafNodes(CommitteeMember member, 
                              RuleDetailVo ruleDetail,
                              TripleMap<String, String, String> strategyMap)
```

#### **递归处理逻辑：**
```
IF ruleDetail == null:
    RETURN
    
IF 是叶子节点 (无子节点):
    IF 有策略键:
        获取策略配置 (ruleType, methodName)
        执行策略方法 calculateScoreByStrategy()
        设置 ruleDetail.finalScore = 计算结果
ELSE 是非叶子节点:
    childrenTotalScore = 0
    FOR 每个子节点 child:
        递归调用 evaluateLeafNodes(member, child)
        childrenTotalScore += child.getFinalScore()
    设置 ruleDetail.finalScore = childrenTotalScore
```

### **4. 策略执行 - calculateScoreByStrategy()**

```java
private Integer calculateScoreByStrategy(CommitteeMember member, 
                                        RuleDetailVo ruleDetail,
                                        Pair<String, String> strategyPair)
```

#### **策略调用逻辑：**
```
获取 ruleType = strategyPair.getKey()      // "BASIC" 或 "REWARD"
获取 methodName = strategyPair.getValue()  // 具体的策略方法名

SWITCH ruleType:
    CASE "BASIC":
        获取缓存的反射方法 getOrCacheMethod()
        调用 basicStrategy.methodName(member, ruleDetail)
        返回 ruleDetail.getFinalScore()
        
    CASE "REWARD":
        获取缓存的反射方法 getOrCacheMethod()
        调用 rewardStrategy.methodName(member, ruleDetail)
        返回 ruleDetail.getFinalScore()
```

### **5. 分数累加 - accumulateScoresByStrategyType()**

```java
private void accumulateScoresByStrategyType(RuleDetailVo ruleDetail,
                                          TripleMap<String, String, String> strategyMap,
                                          ScoreAccumulator accumulator)
```

#### **累加逻辑：**
```
IF ruleDetail == null:
    RETURN
    
IF 是叶子节点 AND 有策略键:
    获取策略配置 strategyPair
    获取 ruleType = strategyPair.getKey()
    获取 finalScore = ruleDetail.getFinalScore()
    
    IF ruleType == "BASIC":
        accumulator.addBasicScore(finalScore)
    ELSE IF ruleType == "REWARD":
        accumulator.addRewardScore(finalScore)
        
ELSE IF 有子节点:
    FOR 每个子节点 child:
        递归调用 accumulateScoresByStrategyType(child)
```

## 🏗️ 数据结构

### **输入数据**
- `memberList`: 委员列表 `List<CommitteeMember>`
- `ruleInfo`: 规则信息树 `RuleInfoVo`
- `strategyMap`: 策略映射 `TripleMap<strategyKey, ruleType, methodName>`

### **中间数据**
- `ScoreAccumulator`: 分数累加器
  ```java
  class ScoreAccumulator {
      Integer basicScore = 0;    // 基础分累计
      Integer rewardScore = 0;   // 奖励分累计
  }
  ```

### **输出数据**
- `RuleScore`: 委员分数结果
  ```java
  class RuleScore {
      Long id;              // 委员ID
      String userName;      // 委员姓名
      Integer basicScore;   // 基础分
      Integer rewardScore;  // 奖励分
      Integer totalScore;   // 总分
      String scoreDetail;   // 分数详情JSON
  }
  ```

## 🔧 关键特性

### **1. 缓存优化**
- **策略实例缓存**: `@PostConstruct` 初始化 BasicStrategy 和 RewardStrategy
- **反射方法缓存**: `methodCache` 避免重复反射调用

### **2. 错误处理**
- **策略执行异常**: 捕获并设置分数为0，记录错误日志
- **空值处理**: 妥善处理 null 值和空策略键
- **数据验证**: 检查委员列表和规则信息的完整性

### **3. 性能考虑**
- **批量处理**: 一次性处理所有委员
- **递归优化**: 高效的树形结构遍历
- **内存管理**: 及时释放临时对象

### **4. 分数分类**
- **策略类型驱动**: 基于 `rule_type` 字段而非 `remark`
- **递归累加**: 正确处理嵌套规则结构
- **类型安全**: 明确的基础分/奖励分分类

## ⚠️ 注意事项

### **1. 数据一致性**
- 确保策略配置的完整性和准确性
- 规则树结构的正确性
- 委员数据的有效性

### **2. 性能监控**
- 监控大数据量下的执行时间
- 关注内存使用情况
- 记录关键执行节点的耗时

### **3. 错误恢复**
- 策略执行失败时的降级处理
- 数据保存失败时的重试机制
- 异常情况下的日志记录

## 📊 执行时序

```
开始 calculateScore(year)
├── 1. 数据准备 (获取委员、规则、策略)
├── 2. 开始计算计时
├── 3. FOR 每个委员:
│   ├── 3.1 FOR 每个顶级规则:
│   │   ├── 3.1.1 重置分数树
│   │   ├── 3.1.2 递归计算叶子节点
│   │   └── 3.1.3 按策略类型累加分数
│   └── 3.2 构建 RuleScore 对象
├── 4. 结束计算计时，输出统计
├── 5. 开始保存计时
├── 6. 批量保存到数据库
├── 7. 结束保存计时
└── 8. 返回影响行数
```

## 总结

`calculateScore` 方法通过清晰的分层设计，实现了高效、准确的规则分数计算：

1. **主入口**: 负责流程控制和性能监控
2. **核心计算**: 遍历委员和规则，协调各个子模块
3. **叶子节点评估**: 递归处理规则树，执行具体策略
4. **策略执行**: 通过反射调用具体的计算方法
5. **分数累加**: 基于策略类型正确分类和累加分数

整个流程保证了计算的正确性、性能的优化和错误的妥善处理。
