package com.ruoyi.common.aop;

import com.ruoyi.common.exception.GlobalException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.security.LoginUser;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class EditDeleteValidationAspect {

    @Before(value = "@annotation(com.ruoyi.common.annotation.ValidateOnEditDelete)")
    public void validateOnEditDelete() {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (!SecurityUtils.isAdmin(loginUser.getUser().getUserId())) {
            throw new GlobalException("无权限进行操作！");
        }
    }
}
