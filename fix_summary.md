# calculateScore 分数为0问题修复总结

## 🎯 问题根因确认

通过日志分析，确认了分数为0的根本原因：

**`@JsonIgnore` 注解导致 `strategyKey` 字段在 JSON 深拷贝时丢失**

```java
// RuleDetailVo.java 中的问题代码
@JsonIgnore
private String strategyKey;
```

当执行深拷贝时：
```java
RuleInfoVo memberRuleInfo = JSON.parseObject(JSON.toJSONString(ruleInfo), RuleInfoVo.class);
```

`@JsonIgnore` 注解会导致 `strategyKey` 字段不被序列化，从而在反序列化后丢失，导致所有叶子节点的策略键都为 `null`，无法执行任何策略计算。

## ✅ 已实施的修复

### 1. 移除深拷贝，直接使用原始对象
```java
// 修复前：使用JSON深拷贝（会丢失@JsonIgnore字段）
RuleInfoVo memberRuleInfo = JSON.parseObject(JSON.toJSONString(ruleInfo), RuleInfoVo.class);

// 修复后：直接使用原始对象
RuleInfoVo memberRuleInfo = ruleInfo;
```

### 2. 添加分数重置机制
```java
// 在处理每个委员前重置所有节点的finalScore
resetFinalScores(ruleDetailVo);

private void resetFinalScores(RuleDetailVo ruleDetail) {
    if (ruleDetail == null) return;
    ruleDetail.setFinalScore(null);
    if (ruleDetail.getChildren() != null) {
        for (RuleDetailVo child : ruleDetail.getChildren()) {
            resetFinalScores(child);
        }
    }
}
```

### 3. 增强调试日志
添加了详细的执行路径日志，包括：
- 规则节点评估过程
- 策略执行详情
- 分数计算跟踪
- 错误处理增强

## 🔍 修复验证

修复后应该能看到以下正常的日志流程：

1. **委员处理开始**
   ```
   开始计算委员: 黄旭
   深拷贝规则信息完成，子规则数量: 2
   ```

2. **规则树遍历**
   ```
   处理顶级规则: 基础分
   评估规则节点: 基础分, 策略键: null, 是否有子节点: true
   评估规则节点: [子规则], 策略键: [具体策略键], 是否有子节点: false
   ```

3. **策略执行**
   ```
   执行策略: 委员=黄旭, 规则=[子规则], 策略键=[策略键]
   开始执行策略计算: 委员=黄旭, 规则=[子规则]
   调用方法: [方法名]
   ```

4. **数据库查询**
   ```
   BasicStrategyMapper 的数据库查询日志
   ```

5. **分数计算完成**
   ```
   策略执行完成: 委员=黄旭, 规则=[子规则], 分数=[具体分数]
   委员 黄旭 原始计算完成: 基础分=[分数], 奖励分=[分数], 总分=[分数]
   ```

## ⚠️ 注意事项

### 1. 线程安全
由于现在直接使用原始规则对象，在并发环境下可能存在线程安全问题。当前通过以下方式缓解：
- 使用串行处理（暂时关闭并行流）
- 每次处理前重置 `finalScore`
- 限制处理数量进行调试

### 2. 临时调试限制
当前代码限制只处理前3个委员：
```java
if (ruleScoreList.size() >= 3) {
    log.info("调试模式：只处理前3个委员");
    break;
}
```

验证修复效果后需要移除此限制。

## 🚀 后续优化计划

### 1. 完善深拷贝机制
```java
// 方案1: 移除@JsonIgnore，使用其他方式隐藏字段
// 方案2: 实现自定义深拷贝方法
// 方案3: 使用BeanUtils等工具进行深拷贝
```

### 2. 恢复并行处理
确认功能正确后，恢复并行流处理以提升性能：
```java
List<RuleScore> ruleScoreList = memberList.parallelStream()
    .map(member -> calculateMemberScore(member, ruleInfo, strategyMethodMap, ruleInfoJson))
    .collect(Collectors.toList());
```

### 3. 移除调试限制
移除只处理前3个委员的限制，处理所有委员。

## 📋 测试检查清单

- [ ] 运行修复后的代码
- [ ] 确认看到 BasicStrategyMapper 的数据库查询日志
- [ ] 验证委员分数不再为0
- [ ] 检查分数计算的正确性
- [ ] 确认所有委员都能正常处理
- [ ] 性能测试（处理时间是否合理）

## 总结

这次问题的核心教训是：**在使用 JSON 序列化进行深拷贝时，要特别注意 `@JsonIgnore` 等注解对字段序列化的影响**。

修复方案虽然简单（移除深拷贝），但需要注意线程安全问题。长期来看，应该实现一个更完善的深拷贝机制，既能保持线程安全，又不会丢失关键字段。
