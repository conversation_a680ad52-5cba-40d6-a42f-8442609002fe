# calculateScore 分数为0问题诊断

## 问题现象
从日志分析可以看出：
1. ✅ 找到了192个委员
2. ✅ 找到了规则信息，有2个子规则（基础分、奖励分）
3. ✅ 策略映射构建成功，8个策略
4. ✅ 策略方法映射构建成功，8个方法
5. ❌ **关键问题：没有看到BasicStrategyMapper的数据库查询日志**
6. ❌ 所有委员分数都是0

## 根因分析

### 可能的原因

#### 1. 规则树结构问题
- 规则可能没有正确的叶子节点
- 策略键(strategyKey)可能为空
- 规则层级结构可能不正确

#### 2. 深拷贝丢失字段
- JSON序列化/反序列化可能丢失了strategyKey字段
- @JsonIgnore注解可能影响了字段拷贝

#### 3. 策略执行路径问题
- 叶子节点判断逻辑可能有误
- 策略键匹配失败

## 已添加的调试措施

### 1. 详细的执行路径日志
```java
// 在evaluateLeafNodesOriginal中添加：
log.debug("评估规则节点: {}, 策略键: {}, 是否有子节点: {}", 
         ruleDetail.getRemark(), ruleDetail.getStrategyKey(), 
         !ObjectUtil.isEmpty(ruleDetail.getChildren()));

// 在calculateScoreByStrategyOriginal中添加：
log.debug("开始执行策略计算: 委员={}, 规则={}, 策略类型={}, 方法名={}", 
         member.getUserName(), ruleDetail.getRemark(), ruleType, methodName);
```

### 2. 分数计算跟踪
```java
// 跟踪每个步骤的分数变化
log.debug("策略执行完成: 委员={}, 规则={}, 分数={}", 
         member.getUserName(), ruleDetail.getRemark(), score);
```

### 3. 限制处理数量
```java
// 只处理前3个委员进行调试
if (ruleScoreList.size() >= 3) {
    log.info("调试模式：只处理前3个委员");
    break;
}
```

## 下一步调试计划

### 1. 运行测试并查看日志
重点关注：
- 是否有"评估规则节点"的日志
- 策略键是否为空
- 是否进入了叶子节点处理逻辑
- 是否调用了策略计算方法

### 2. 检查规则数据结构
如果没有看到预期的日志，需要检查：
- RuleDetailVo的strategyKey字段是否正确设置
- 规则树的层级结构是否正确
- 是否存在真正的叶子节点

### 3. 验证JSON序列化
检查深拷贝是否丢失关键字段：
```java
// 对比原始和拷贝后的对象
log.info("原始规则strategyKey: {}", ruleInfo.getChildren().get(0).getStrategyKey());
log.info("拷贝后规则strategyKey: {}", memberRuleInfo.getChildren().get(0).getStrategyKey());
```

## 预期的正常日志流程

如果一切正常，应该看到以下日志序列：
1. "开始计算委员: 黄旭"
2. "深拷贝规则信息完成，子规则数量: 2"
3. "处理顶级规则: 基础分"
4. "评估规则节点: 基础分, 策略键: null, 是否有子节点: true"
5. 对每个子节点：
   - "评估规则节点: [子规则名], 策略键: [策略键], 是否有子节点: false"
   - "执行策略: 委员=黄旭, 规则=[子规则名], 策略键=[策略键]"
   - "开始执行策略计算: 委员=黄旭, 规则=[子规则名]"
   - BasicStrategyMapper的数据库查询日志
   - "策略执行完成: 委员=黄旭, 规则=[子规则名], 分数=[分数]"

## 可能的修复方案

### 方案1: 检查@JsonIgnore注解
```java
// 在RuleDetailVo中，strategyKey字段有@JsonIgnore注解
@JsonIgnore
private String strategyKey;
```
这可能导致JSON序列化时丢失该字段。

### 方案2: 使用BeanUtils深拷贝
```java
// 替换JSON深拷贝
RuleInfoVo memberRuleInfo = new RuleInfoVo();
BeanUtils.copyProperties(ruleInfo, memberRuleInfo);
// 递归拷贝子节点...
```

### 方案3: 直接使用原始对象
```java
// 暂时不进行深拷贝，直接使用原始对象
// 注意线程安全问题
for (RuleDetailVo ruleDetailVo : ruleInfo.getChildren()) {
    // 重置finalScore
    resetFinalScores(ruleDetailVo);
    evaluateLeafNodesOriginal(member, ruleDetailVo, strategyMap);
    // ...
}
```

## 总结

当前最可能的问题是 **@JsonIgnore注解导致strategyKey字段在深拷贝时丢失**，从而导致所有叶子节点的策略键都为空，无法执行任何策略计算。

需要通过运行测试并查看详细日志来确认这个假设。
