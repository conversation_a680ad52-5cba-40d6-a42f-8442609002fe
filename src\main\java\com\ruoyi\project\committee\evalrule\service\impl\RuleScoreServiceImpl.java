package com.ruoyi.project.committee.evalrule.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.collections.TripleMap;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.archive.mapper.CommitteeMemberMapper;
import com.ruoyi.project.committee.evalrule.domain.RuleScore;
import com.ruoyi.project.committee.evalrule.domain.dto.RuleScorePageDto;
import com.ruoyi.project.committee.evalrule.domain.vo.RuleDetailVo;
import com.ruoyi.project.committee.evalrule.domain.vo.RuleInfoVo;
import com.ruoyi.project.committee.evalrule.mapper.RuleScoreMapper;
import com.ruoyi.project.committee.evalrule.service.IRuleInfoService;
import com.ruoyi.project.committee.evalrule.service.IRuleScoreService;
import com.ruoyi.project.committee.evalrule.service.IRuleStrategyService;
import com.ruoyi.project.committee.evalrule.strategy.BasicStrategy;
import com.ruoyi.project.committee.evalrule.strategy.RewardStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RuleScoreServiceImpl extends ServiceImpl<RuleScoreMapper, RuleScore> implements IRuleScoreService {

    @Resource
    private RuleScoreMapper ruleScoreMapper;

    @Resource
    private IRuleStrategyService ruleStrategyService;

    @Resource
    private IRuleInfoService ruleInfoService;

    @Resource
    private CommitteeMemberMapper committeeMemberMapper;

    // 缓存策略实例，避免重复获取Spring Bean
    private BasicStrategy basicStrategy;
    private RewardStrategy rewardStrategy;

    // 缓存反射方法，避免重复反射调用
    private final Map<String, Method> methodCache = new ConcurrentHashMap<>();

    // 常量定义，避免字符串比较
    private static final String BASIC_SCORE_TYPE = "基础分";
    private static final String REWARD_SCORE_TYPE = "奖励分";
    private static final String STRATEGY_TYPE_BASIC = "BASIC";
    private static final String STRATEGY_TYPE_REWARD = "REWARD";

    @PostConstruct
    public void init() {
        // 初始化时获取策略实例，避免运行时重复获取
        this.basicStrategy = SpringUtils.getBean(BasicStrategy.class);
        this.rewardStrategy = SpringUtils.getBean(RewardStrategy.class);
    }


    @Override
    public IPage<RuleScore> selectRuleScorePage(RuleScorePageDto pageDto) {
        if (ObjectUtil.isNull(pageDto.getYear())) {
            pageDto.setYear(DateUtil.thisYear());
        }
        Page<RuleScore> page = new Page<>(pageDto.getCurrentPage(), pageDto.getPageSize());
        return ruleScoreMapper.selectRuleScorePage(page, pageDto);
    }

    @Override
    public RuleScore getById(String id) {
        return ruleScoreMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer calculateScore(Integer year) {
        // 获取委员
        List<CommitteeMember> memberList = committeeMemberMapper.selectMemberByYear(year);
        if (ObjectUtil.isEmpty(memberList)) {
            throw new ServiceException("未找到" + year + "年度的委员信息.");
        }

        // 获取规则
        RuleInfoVo ruleInfo = ruleInfoService.selectRuleInfoByYear(year);
        if (ObjectUtil.isEmpty(ruleInfo)) {
            throw new ServiceException("未找到" + year + "年度的规则信息.");
        }

        // 获取策略映射（一次性获取，避免重复查询）
        TripleMap<String, String, String> strategyMap = ruleStrategyService.getStrategyMap();

        // 预编译策略方法映射，避免运行时反射
        Map<String, BiFunction<CommitteeMember, RuleDetailVo, Integer>> strategyMethodMap =
            buildStrategyMethodMap(strategyMap);

        // 预序列化规则信息，避免重复序列化
        String ruleInfoJson = JSON.toJSONString(ruleInfo);

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("计算规则分数");

        // 使用并行流处理委员列表，提高计算效率
        List<RuleScore> ruleScoreList = memberList.parallelStream()
            .map(member -> calculateMemberScore(member, ruleInfo, strategyMethodMap, ruleInfoJson))
            .collect(Collectors.toList());

        stopWatch.stop();
        log.info("计算规则分数完成，耗时：{} ms", stopWatch.getTotalTimeMillis());

        stopWatch.start("保存规则分数");
        Integer affectRows = upsertRuleScoresBatch(ruleScoreList);
        stopWatch.stop();
        log.info("保存规则分数完成，耗时：{} ms", stopWatch.getTotalTimeMillis());

        return affectRows;
    }

    /**
     * 计算单个委员的分数
     */
    private RuleScore calculateMemberScore(CommitteeMember member, RuleInfoVo ruleInfo,
                                         Map<String, BiFunction<CommitteeMember, RuleDetailVo, Integer>> strategyMethodMap,
                                         String ruleInfoJson) {
        // 深拷贝规则信息，避免并发修改
        RuleInfoVo memberRuleInfo = JSON.parseObject(JSON.toJSONString(ruleInfo), RuleInfoVo.class);

        Integer basicScore = 0;
        Integer rewardScore = 0;

        for (RuleDetailVo ruleDetailVo : memberRuleInfo.getChildren()) {
            evaluateLeafNodesOptimized(member, ruleDetailVo, strategyMethodMap);

            // 使用常量比较，提高效率
            if (BASIC_SCORE_TYPE.equals(ruleDetailVo.getRemark())) {
                basicScore += Optional.ofNullable(ruleDetailVo.getFinalScore()).orElse(0);
            } else if (REWARD_SCORE_TYPE.equals(ruleDetailVo.getRemark())) {
                rewardScore += Optional.ofNullable(ruleDetailVo.getFinalScore()).orElse(0);
            }
        }

        // 构建结果对象
        RuleScore ruleScore = new RuleScore();
        ruleScore.setId(member.getId());
        ruleScore.setYear(Integer.valueOf(member.getYear()));
        ruleScore.setUserId(Long.valueOf(member.getUserId()));
        ruleScore.setUserName(member.getUserName());
        ruleScore.setNumberId(member.getNumberId());
        ruleScore.setUnitPost(member.getUnitPost());
        ruleScore.setBasicScore(basicScore);
        ruleScore.setRewardScore(rewardScore);
        ruleScore.setTotalScore(basicScore + rewardScore);
        ruleScore.setScoreDetail(ruleInfoJson);

        return ruleScore;
    }

    /**
     * 构建策略方法映射，预编译反射方法
     */
    private Map<String, BiFunction<CommitteeMember, RuleDetailVo, Integer>> buildStrategyMethodMap(
            TripleMap<String, String, String> strategyMap) {
        Map<String, BiFunction<CommitteeMember, RuleDetailVo, Integer>> methodMap = new HashMap<>();

        for (String strategyKey : strategyMap.keySet()) {
            Pair<String, String> strategyPair = strategyMap.get(strategyKey);
            if (strategyPair == null) continue;

            String ruleType = strategyPair.getKey();
            String methodName = strategyPair.getValue();

            if (methodName == null) continue;

            try {
                Method method = getOrCacheMethod(ruleType, methodName);
                BiFunction<CommitteeMember, RuleDetailVo, Integer> function =
                    createStrategyFunction(ruleType, method);
                methodMap.put(strategyKey, function);
            } catch (Exception e) {
                log.error("Failed to build strategy method for key: {}, method: {}",
                         strategyKey, methodName, e);
            }
        }

        return methodMap;
    }

    /**
     * 获取或缓存反射方法
     */
    private Method getOrCacheMethod(String ruleType, String methodName) throws NoSuchMethodException {
        String cacheKey = ruleType + ":" + methodName;
        return methodCache.computeIfAbsent(cacheKey, key -> {
            try {
                if (STRATEGY_TYPE_BASIC.equals(ruleType)) {
                    return basicStrategy.getClass().getMethod(methodName,
                           CommitteeMember.class, RuleDetailVo.class);
                } else if (STRATEGY_TYPE_REWARD.equals(ruleType)) {
                    return rewardStrategy.getClass().getMethod(methodName,
                           CommitteeMember.class, RuleDetailVo.class);
                }
                throw new IllegalArgumentException("Unknown strategy type: " + ruleType);
            } catch (NoSuchMethodException e) {
                throw new RuntimeException("Method not found: " + methodName, e);
            }
        });
    }

    /**
     * 创建策略执行函数
     */
    private BiFunction<CommitteeMember, RuleDetailVo, Integer> createStrategyFunction(
            String ruleType, Method method) {
        return (member, ruleDetail) -> {
            try {
                if (STRATEGY_TYPE_BASIC.equals(ruleType)) {
                    method.invoke(basicStrategy, member, ruleDetail);
                } else if (STRATEGY_TYPE_REWARD.equals(ruleType)) {
                    method.invoke(rewardStrategy, member, ruleDetail);
                }
                return Optional.ofNullable(ruleDetail.getFinalScore()).orElse(0);
            } catch (Exception e) {
                log.error("Strategy execution failed for method: {}", method.getName(), e);
                return 0;
            }
        };
    }

    /**
     * 优化后的叶子节点评估方法，使用迭代代替递归
     */
    private void evaluateLeafNodesOptimized(CommitteeMember member, RuleDetailVo ruleDetail,
                                          Map<String, BiFunction<CommitteeMember, RuleDetailVo, Integer>> strategyMethodMap) {
        if (ruleDetail == null) return;

        // 使用栈进行迭代，避免递归调用栈溢出
        Deque<RuleDetailVo> stack = new ArrayDeque<>();
        stack.push(ruleDetail);

        // 后序遍历，确保子节点先于父节点计算
        List<RuleDetailVo> postOrder = new ArrayList<>();
        Set<RuleDetailVo> visited = new HashSet<>();

        while (!stack.isEmpty()) {
            RuleDetailVo current = stack.peek();

            if (ObjectUtil.isEmpty(current.getChildren()) || visited.contains(current)) {
                // 叶子节点或已访问过的节点
                postOrder.add(stack.pop());
                visited.add(current);
            } else {
                // 将子节点压入栈
                for (int i = current.getChildren().size() - 1; i >= 0; i--) {
                    stack.push(current.getChildren().get(i));
                }
                visited.add(current);
            }
        }

        // 按后序遍历顺序计算分数
        for (RuleDetailVo node : postOrder) {
            if (ObjectUtil.isEmpty(node.getChildren())) {
                // 叶子节点：执行策略计算
                calculateLeafNodeScore(member, node, strategyMethodMap);
            } else {
                // 非叶子节点：汇总子节点分数
                int childrenTotalScore = node.getChildren().stream()
                    .mapToInt(child -> Optional.ofNullable(child.getFinalScore()).orElse(0))
                    .sum();
                node.setFinalScore(childrenTotalScore);
            }
        }
    }

    /**
     * 计算叶子节点分数
     */
    private void calculateLeafNodeScore(CommitteeMember member, RuleDetailVo ruleDetail,
                                      Map<String, BiFunction<CommitteeMember, RuleDetailVo, Integer>> strategyMethodMap) {
        if (ruleDetail.getStrategyKey() == null) {
            ruleDetail.setFinalScore(0);
            return;
        }

        BiFunction<CommitteeMember, RuleDetailVo, Integer> strategyFunction =
            strategyMethodMap.get(ruleDetail.getStrategyKey());

        if (strategyFunction == null) {
            ruleDetail.setFinalScore(0);
            return;
        }

        try {
            Integer score = strategyFunction.apply(member, ruleDetail);
            ruleDetail.setFinalScore(score);
        } catch (Exception e) {
            log.error("Strategy execution failed for key: {}", ruleDetail.getStrategyKey(), e);
            ruleDetail.setFinalScore(0);
        }
    }


    private Integer calculateScoreByStrategy(CommitteeMember member, RuleDetailVo ruleDetail,
                                             Pair<String, String> strategyPair) throws Exception {
        String ruleType = strategyPair.getKey();
        String methodName = strategyPair.getValue();

        if (methodName == null) {
            return 0;
        }

        switch (ruleType) {
            case "BASIC":
                BasicStrategy basicStrategy = SpringUtils.getBean(BasicStrategy.class);
                Method basicMethod = basicStrategy.getClass().getMethod(methodName, CommitteeMember.class, RuleDetailVo.class);
                basicMethod.invoke(basicStrategy, member, ruleDetail);
                return ruleDetail.getFinalScore();
            case "REWARD":
                RewardStrategy rewardStrategy = SpringUtils.getBean(RewardStrategy.class);
                Method rewardMethod = rewardStrategy.getClass().getMethod(methodName, CommitteeMember.class, RuleDetailVo.class);
                rewardMethod.invoke(rewardStrategy, member, ruleDetail);
                return ruleDetail.getFinalScore();
            default:
                log.warn("未知策略类型: {}", ruleType);
                return 0;
        }
    }

    private Integer upsertRuleScoresBatch(List<RuleScore> ruleScoreList) {
        if (ObjectUtil.isEmpty(ruleScoreList)) {
            return 0;
        }

        // 1. 计算安全并行度
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        int safeParallelism = Math.min(4, Math.max(2, availableProcessors > 0 ? availableProcessors - 1 : 1));

        // 3. 创建线程池（添加线程池名称便于监控）
        ForkJoinPool privatePool = new ForkJoinPool(safeParallelism,
                ForkJoinPool.defaultForkJoinWorkerThreadFactory,
                null,
                false
        );

        try {
            // 4. 动态批次大小（基于数据量和并行度）
            int batchSize = Math.min(50, ruleScoreList.size() / safeParallelism + 1);

            // 5. 执行并行处理（显式处理sum结果）
            return privatePool.submit(() ->
                    Lists.partition(ruleScoreList, batchSize)
                            .parallelStream()
                            .mapToInt(batch -> {
                                try {
                                    return ruleScoreMapper.upsertRuleScoresBatch(batch);
                                } catch (Exception e) {
                                    log.error("Batch failed (size={}, firstId={})",
                                            batch.size(), batch.get(0).getId(), e);
                                    return batch.stream()
                                            .mapToInt(item -> ruleScoreMapper.upsertRuleScoresBatch(
                                                    Collections.singletonList(item)
                                            ))
                                            .sum();
                                }
                            })
                            .sum()
            ).get(30, TimeUnit.SECONDS);

        } catch (TimeoutException e) {
            log.error("Batch timeout after 30s, dataSize={}", ruleScoreList.size(), e);
            throw new ServiceException("BATCH_PROCESS_TIMEOUT");
        } catch (Exception e) {
            log.error("Unexpected batch error, parallelism={}", safeParallelism, e);
            throw new ServiceException("BATCH_PROCESS_FAILED");
        } finally {
            // 6. 强制关闭线程池（终止等待）
            privatePool.shutdown();
            try {
                if (!privatePool.awaitTermination(5, TimeUnit.SECONDS)) {
                    privatePool.shutdownNow();
                }
            } catch (InterruptedException e) {
                privatePool.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
