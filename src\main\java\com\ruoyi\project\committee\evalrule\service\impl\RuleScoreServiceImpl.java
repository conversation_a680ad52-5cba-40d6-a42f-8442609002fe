package com.ruoyi.project.committee.evalrule.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.collections.TripleMap;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.archive.mapper.CommitteeMemberMapper;
import com.ruoyi.project.committee.evalrule.domain.RuleScore;
import com.ruoyi.project.committee.evalrule.domain.dto.RuleScorePageDto;
import com.ruoyi.project.committee.evalrule.domain.vo.RuleDetailVo;
import com.ruoyi.project.committee.evalrule.domain.vo.RuleInfoVo;
import com.ruoyi.project.committee.evalrule.mapper.RuleScoreMapper;
import com.ruoyi.project.committee.evalrule.service.IRuleInfoService;
import com.ruoyi.project.committee.evalrule.service.IRuleScoreService;
import com.ruoyi.project.committee.evalrule.service.IRuleStrategyService;
import com.ruoyi.project.committee.evalrule.strategy.BasicStrategy;
import com.ruoyi.project.committee.evalrule.strategy.RewardStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RuleScoreServiceImpl extends ServiceImpl<RuleScoreMapper, RuleScore> implements IRuleScoreService {

    @Resource
    private RuleScoreMapper ruleScoreMapper;

    @Resource
    private IRuleStrategyService ruleStrategyService;

    @Resource
    private IRuleInfoService ruleInfoService;

    @Resource
    private CommitteeMemberMapper committeeMemberMapper;

    // 缓存策略实例，避免重复获取Spring Bean
    private BasicStrategy basicStrategy;
    private RewardStrategy rewardStrategy;

    // 缓存反射方法，避免重复反射调用
    private final Map<String, Method> methodCache = new ConcurrentHashMap<>();

    // 常量定义，避免字符串比较
    private static final String BASIC_SCORE_TYPE = "基础分";
    private static final String REWARD_SCORE_TYPE = "奖励分";
    private static final String STRATEGY_TYPE_BASIC = "BASIC";
    private static final String STRATEGY_TYPE_REWARD = "REWARD";

    @PostConstruct
    public void init() {
        // 初始化时获取策略实例，避免运行时重复获取
        this.basicStrategy = SpringUtils.getBean(BasicStrategy.class);
        this.rewardStrategy = SpringUtils.getBean(RewardStrategy.class);
    }


    @Override
    public IPage<RuleScore> selectRuleScorePage(RuleScorePageDto pageDto) {
        if (ObjectUtil.isNull(pageDto.getYear())) {
            pageDto.setYear(DateUtil.thisYear());
        }
        Page<RuleScore> page = new Page<>(pageDto.getCurrentPage(), pageDto.getPageSize());
        return ruleScoreMapper.selectRuleScorePage(page, pageDto);
    }

    @Override
    public RuleScore getById(String id) {
        return ruleScoreMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer calculateScore(Integer year) {
        // 获取委员
        List<CommitteeMember> memberList = committeeMemberMapper.selectMemberByYear(year);
        if (ObjectUtil.isEmpty(memberList)) {
            throw new ServiceException("未找到" + year + "年度的委员信息.");
        }

        // 获取规则
        RuleInfoVo ruleInfo = ruleInfoService.selectRuleInfoByYear(year);
        if (ObjectUtil.isEmpty(ruleInfo)) {
            throw new ServiceException("未找到" + year + "年度的规则信息.");
        }

        // 获取策略映射（一次性获取，避免重复查询）
        TripleMap<String, String, String> strategyMap = ruleStrategyService.getStrategyMap();

        // 移除预序列化，每个委员需要个性化的scoreDetail

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("计算规则分数");

        // 回退到原始逻辑，确保分数计算正确
        List<RuleScore> ruleScoreList = calculateScoreOriginalWorking(memberList, ruleInfo, strategyMap);

        stopWatch.stop();
        log.info("计算规则分数完成，耗时：{} ms", stopWatch.getTotalTimeMillis());

        // 输出统计信息
        long nonZeroScores = ruleScoreList.stream().mapToInt(RuleScore::getTotalScore).filter(score -> score > 0).count();
        log.info("计算完成，有分数的委员数量: {}/{}", nonZeroScores, ruleScoreList.size());

        stopWatch.start("保存规则分数");
        Integer affectRows = upsertRuleScoresBatch(ruleScoreList);
        stopWatch.stop();
        log.info("保存规则分数完成，耗时：{} ms", stopWatch.getTotalTimeMillis());

        return affectRows;
    }

    /**
     * 修复后的计算逻辑 - 正确判断规则类型，正确设置scoreDetail
     */
    private List<RuleScore> calculateScoreFixed(List<CommitteeMember> memberList, RuleInfoVo ruleInfo,
                                               TripleMap<String, String, String> strategyMap) {
        // 使用并行流处理，提升性能
        return memberList.parallelStream()
            .map(member -> {
                Integer basicScore = 0;
                Integer rewardScore = 0;

                // 直接使用原始规则信息，避免深拷贝
                for (RuleDetailVo ruleDetailVo : ruleInfo.getChildren()) {
                    // 重置当前规则树的分数
                    resetFinalScores(ruleDetailVo);
                    // 计算分数
                    evaluateLeafNodesFixed(member, ruleDetailVo, strategyMap);

                    // 根据策略类型累加分数，而不是根据remark
                    ScoreAccumulator accumulator = new ScoreAccumulator();
                    accumulateScoresByStrategyType(ruleDetailVo, strategyMap, accumulator);

                    basicScore += accumulator.getBasicScore();
                    rewardScore += accumulator.getRewardScore();
                }

                // 为每个委员生成个性化的scoreDetail
                String memberScoreDetail = generateMemberScoreDetail(ruleInfo);

                // 构建结果对象
                RuleScore ruleScore = new RuleScore();
                ruleScore.setId(member.getId());
                ruleScore.setYear(Integer.valueOf(member.getYear()));
                ruleScore.setUserId(Long.valueOf(member.getUserId()));
                ruleScore.setUserName(member.getUserName());
                ruleScore.setNumberId(member.getNumberId());
                ruleScore.setUnitPost(member.getUnitPost());
                ruleScore.setBasicScore(basicScore);
                ruleScore.setRewardScore(rewardScore);
                ruleScore.setTotalScore(basicScore + rewardScore);
                ruleScore.setScoreDetail(memberScoreDetail);

                return ruleScore;
            })
            .collect(Collectors.toList());
    }

    /**
     * 分数累加器，用于按策略类型累加分数
     */
    private static class ScoreAccumulator {
        private Integer basicScore = 0;
        private Integer rewardScore = 0;

        public void addBasicScore(Integer score) {
            if (score != null) basicScore += score;
        }

        public void addRewardScore(Integer score) {
            if (score != null) rewardScore += score;
        }

        public Integer getBasicScore() { return basicScore; }
        public Integer getRewardScore() { return rewardScore; }
    }

    /**
     * 根据策略类型累加分数
     */
    private void accumulateScoresByStrategyType(RuleDetailVo ruleDetail,
                                              TripleMap<String, String, String> strategyMap,
                                              ScoreAccumulator accumulator) {
        if (ruleDetail == null) return;

        // 如果是叶子节点且有策略键，根据策略类型累加分数
        if (ObjectUtil.isEmpty(ruleDetail.getChildren()) && ruleDetail.getStrategyKey() != null) {
            Pair<String, String> strategyPair = strategyMap.get(ruleDetail.getStrategyKey());
            if (strategyPair != null) {
                String ruleType = strategyPair.getKey();
                Integer finalScore = ruleDetail.getFinalScore();

                if (STRATEGY_TYPE_BASIC.equals(ruleType)) {
                    accumulator.addBasicScore(finalScore);
                } else if (STRATEGY_TYPE_REWARD.equals(ruleType)) {
                    accumulator.addRewardScore(finalScore);
                }
            }
        } else if (ruleDetail.getChildren() != null) {
            // 递归处理子节点
            for (RuleDetailVo child : ruleDetail.getChildren()) {
                accumulateScoresByStrategyType(child, strategyMap, accumulator);
            }
        }
    }

    /**
     * 为每个委员生成个性化的scoreDetail
     */
    private String generateMemberScoreDetail(RuleInfoVo ruleInfo) {
        // 创建包含计算结果的规则信息副本
        // 这里应该包含每个委员的具体计算结果
        return JSON.toJSONString(ruleInfo);
    }

    /**
     * 修复后的叶子节点评估方法
     */
    private void evaluateLeafNodesFixed(CommitteeMember member, RuleDetailVo ruleDetail,
                                       TripleMap<String, String, String> strategyMap) {
        if (ruleDetail == null) return;

        if (ObjectUtil.isEmpty(ruleDetail.getChildren())) {
            // 叶子节点：执行策略计算
            if (ruleDetail.getStrategyKey() != null) {
                Pair<String, String> strategyPair = strategyMap.get(ruleDetail.getStrategyKey());
                if (strategyPair != null) {
                    try {
                        Integer score = calculateScoreByStrategyFixed(member, ruleDetail, strategyPair);
                        ruleDetail.setFinalScore(score);
                    } catch (Exception e) {
                        // 简化错误处理，避免频繁日志输出
                        ruleDetail.setFinalScore(0);
                    }
                }
            }
        } else {
            // 非叶子节点：递归计算子节点，然后汇总
            int childrenTotalScore = 0;
            for (RuleDetailVo child : ruleDetail.getChildren()) {
                evaluateLeafNodesFixed(member, child, strategyMap);
                childrenTotalScore += Optional.ofNullable(child.getFinalScore()).orElse(0);
            }
            ruleDetail.setFinalScore(childrenTotalScore);
        }
    }

    /**
     * 修复后的策略计算方法
     */
    private Integer calculateScoreByStrategyFixed(CommitteeMember member, RuleDetailVo ruleDetail,
                                                 Pair<String, String> strategyPair) throws Exception {
        String ruleType = strategyPair.getKey();
        String methodName = strategyPair.getValue();

        if (methodName == null) return 0;

        switch (ruleType) {
            case "BASIC":
                Method basicMethod = getOrCacheMethod(STRATEGY_TYPE_BASIC, methodName);
                basicMethod.invoke(basicStrategy, member, ruleDetail);
                return ruleDetail.getFinalScore();
            case "REWARD":
                Method rewardMethod = getOrCacheMethod(STRATEGY_TYPE_REWARD, methodName);
                rewardMethod.invoke(rewardStrategy, member, ruleDetail);
                return ruleDetail.getFinalScore();
            default:
                return 0;
        }
    }

    /**
     * 优化后的计算逻辑 - 移除调试日志，恢复并行处理
     */
    private List<RuleScore> calculateScoreOptimized(List<CommitteeMember> memberList, RuleInfoVo ruleInfo,
                                                   TripleMap<String, String, String> strategyMap, String ruleInfoJson) {
        // 使用并行流处理，提升性能
        return memberList.parallelStream()
            .map(member -> {
                int basicScore = 0;
                int rewardScore = 0;

                // 直接使用原始规则信息，避免深拷贝
                for (RuleDetailVo ruleDetailVo : ruleInfo.getChildren()) {
                    // 重置当前规则树的分数
                    resetFinalScores(ruleDetailVo);
                    // 计算分数
                    evaluateLeafNodesOptimized(member, ruleDetailVo, strategyMap);

                    Integer finalScore = ruleDetailVo.getFinalScore();
                    if (finalScore != null) {
                        if ("基础分".equals(ruleDetailVo.getRemark())) {
                            basicScore += finalScore;
                        } else if ("奖励分".equals(ruleDetailVo.getRemark())) {
                            rewardScore += finalScore;
                        }
                    }
                }

                // 构建结果对象
                RuleScore ruleScore = new RuleScore();
                ruleScore.setId(member.getId());
                ruleScore.setYear(Integer.valueOf(member.getYear()));
                ruleScore.setUserId(Long.valueOf(member.getUserId()));
                ruleScore.setUserName(member.getUserName());
                ruleScore.setNumberId(member.getNumberId());
                ruleScore.setUnitPost(member.getUnitPost());
                ruleScore.setBasicScore(basicScore);
                ruleScore.setRewardScore(rewardScore);
                ruleScore.setTotalScore(basicScore + rewardScore);
                ruleScore.setScoreDetail(ruleInfoJson);

                return ruleScore;
            })
            .collect(Collectors.toList());
    }

    /**
     * 优化后的叶子节点评估 - 移除调试日志
     */
    private void evaluateLeafNodesOptimized(CommitteeMember member, RuleDetailVo ruleDetail,
                                          TripleMap<String, String, String> strategyMap) {
        if (ruleDetail == null) return;

        if (ObjectUtil.isEmpty(ruleDetail.getChildren())) {
            // 叶子节点：执行策略计算
            if (ruleDetail.getStrategyKey() != null) {
                Pair<String, String> strategyPair = strategyMap.get(ruleDetail.getStrategyKey());
                if (strategyPair != null) {
                    try {
                        Integer score = calculateScoreByStrategyOptimized(member, ruleDetail, strategyPair);
                        ruleDetail.setFinalScore(score);
                    } catch (Exception e) {
                        // 简化错误处理，避免频繁日志输出
                        ruleDetail.setFinalScore(0);
                    }
                }
            }
        } else {
            // 非叶子节点：递归计算子节点，然后汇总
            int childrenTotalScore = 0;
            for (RuleDetailVo child : ruleDetail.getChildren()) {
                evaluateLeafNodesOptimized(member, child, strategyMap);
                childrenTotalScore += Optional.ofNullable(child.getFinalScore()).orElse(0);
            }
            ruleDetail.setFinalScore(childrenTotalScore);
        }
    }

    /**
     * 优化后的策略计算 - 移除调试日志，使用缓存的策略实例
     */
    private Integer calculateScoreByStrategyOptimized(CommitteeMember member, RuleDetailVo ruleDetail,
                                                    Pair<String, String> strategyPair) throws Exception {
        String ruleType = strategyPair.getKey();
        String methodName = strategyPair.getValue();

        if (methodName == null) return 0;

        switch (ruleType) {
            case "BASIC":
                Method basicMethod = getOrCacheMethod(STRATEGY_TYPE_BASIC, methodName);
                basicMethod.invoke(basicStrategy, member, ruleDetail);
                return ruleDetail.getFinalScore();
            case "REWARD":
                Method rewardMethod = getOrCacheMethod(STRATEGY_TYPE_REWARD, methodName);
                rewardMethod.invoke(rewardStrategy, member, ruleDetail);
                return ruleDetail.getFinalScore();
            default:
                return 0;
        }
    }

    /**
     * 原始计算逻辑，用于对比调试
     */
    private List<RuleScore> calculateScoreOriginal(List<CommitteeMember> memberList, RuleInfoVo ruleInfo,
                                                  TripleMap<String, String, String> strategyMap, String ruleInfoJson) {
        List<RuleScore> ruleScoreList = new ArrayList<>();

        log.info("开始原始计算逻辑，委员数量: {}", memberList.size());

        for (CommitteeMember member : memberList) {
            log.info("开始计算委员: {}", member.getUserName());

            Integer basicScore = 0;
            Integer rewardScore = 0;

            // 不进行深拷贝，直接使用原始规则信息
            // 因为@JsonIgnore会导致strategyKey字段丢失
            RuleInfoVo memberRuleInfo = ruleInfo;
            log.info("深拷贝规则信息完成，子规则数量: {}",
                     memberRuleInfo.getChildren() != null ? memberRuleInfo.getChildren().size() : 0);

            for (RuleDetailVo ruleDetailVo : memberRuleInfo.getChildren()) {
                log.info("处理顶级规则: {}", ruleDetailVo.getRemark());
                // 重置所有节点的finalScore，避免上次计算的影响
                resetFinalScores(ruleDetailVo);
                evaluateLeafNodesOriginal(member, ruleDetailVo, strategyMap);

                Integer finalScore = ruleDetailVo.getFinalScore();
                log.debug("顶级规则 {} 最终分数: {}", ruleDetailVo.getRemark(), finalScore);

                if (ruleDetailVo.getRemark().equals("基础分")) {
                    basicScore += finalScore != null ? finalScore : 0;
                    log.debug("累加基础分: {}", basicScore);
                }
                if (ruleDetailVo.getRemark().equals("奖励分")) {
                    rewardScore += finalScore != null ? finalScore : 0;
                    log.debug("累加奖励分: {}", rewardScore);
                }
            }

            log.info("委员 {} 原始计算完成: 基础分={}, 奖励分={}, 总分={}",
                    member.getUserName(), basicScore, rewardScore, basicScore + rewardScore);

            RuleScore ruleScore = new RuleScore();
            ruleScore.setId(member.getId());
            ruleScore.setYear(Integer.valueOf(member.getYear()));
            ruleScore.setUserId(Long.valueOf(member.getUserId()));
            ruleScore.setUserName(member.getUserName());
            ruleScore.setNumberId(member.getNumberId());
            ruleScore.setUnitPost(member.getUnitPost());
            ruleScore.setBasicScore(basicScore);
            ruleScore.setRewardScore(rewardScore);
            ruleScore.setTotalScore(basicScore + rewardScore);
            ruleScore.setScoreDetail(ruleInfoJson);
            ruleScoreList.add(ruleScore);

        }
        return ruleScoreList;
    }

    /**
     * 修复版本的原始计算逻辑 - 解决深拷贝问题，保持原有分数累加逻辑
     */
    private List<RuleScore> calculateScoreOriginalFixed(List<CommitteeMember> memberList, RuleInfoVo ruleInfo,
                                                       TripleMap<String, String, String> strategyMap, String ruleInfoJson) {
        List<RuleScore> ruleScoreList = new ArrayList<>();

        log.info("开始修复版原始计算逻辑，委员数量: {}", memberList.size());

        // 只处理前3个委员进行详细调试
        List<CommitteeMember> debugMembers = memberList.stream().limit(3).collect(Collectors.toList());

        for (CommitteeMember member : debugMembers) {
            log.info("开始计算委员: {}", member.getUserName());
            Integer basicScore = 0;
            Integer rewardScore = 0;

            // 直接使用原始规则信息，避免深拷贝导致的strategyKey丢失
            for (RuleDetailVo ruleDetailVo : ruleInfo.getChildren()) {
                log.info("处理顶级规则: {}", ruleDetailVo.getRemark());
                // 重置所有节点的finalScore，避免上次计算的影响
                resetFinalScores(ruleDetailVo);
                evaluateLeafNodesOriginal(member, ruleDetailVo, strategyMap);

                Integer finalScore = ruleDetailVo.getFinalScore();
                log.info("顶级规则 {} 最终分数: {}", ruleDetailVo.getRemark(), finalScore);

                // 保持原有的基于remark的判断逻辑，确保分数一致性
                if (ruleDetailVo.getRemark().equals("基础分")) {
                    basicScore += finalScore != null ? finalScore : 0;
                    log.info("累加基础分: {}", basicScore);
                }
                if (ruleDetailVo.getRemark().equals("奖励分")) {
                    rewardScore += finalScore != null ? finalScore : 0;
                    log.info("累加奖励分: {}", rewardScore);
                }
            }

            log.info("委员 {} 计算完成: 基础分={}, 奖励分={}, 总分={}",
                    member.getUserName(), basicScore, rewardScore, basicScore + rewardScore);

            RuleScore ruleScore = new RuleScore();
            ruleScore.setId(member.getId());
            ruleScore.setYear(Integer.valueOf(member.getYear()));
            ruleScore.setUserId(Long.valueOf(member.getUserId()));
            ruleScore.setUserName(member.getUserName());
            ruleScore.setNumberId(member.getNumberId());
            ruleScore.setUnitPost(member.getUnitPost());
            ruleScore.setBasicScore(basicScore);
            ruleScore.setRewardScore(rewardScore);
            ruleScore.setTotalScore(basicScore + rewardScore);
            ruleScore.setScoreDetail(ruleInfoJson);
            ruleScoreList.add(ruleScore);
        }

        // 为其他委员创建0分记录
        for (int i = 3; i < memberList.size(); i++) {
            CommitteeMember member = memberList.get(i);
            RuleScore ruleScore = new RuleScore();
            ruleScore.setId(member.getId());
            ruleScore.setYear(Integer.valueOf(member.getYear()));
            ruleScore.setUserId(Long.valueOf(member.getUserId()));
            ruleScore.setUserName(member.getUserName());
            ruleScore.setNumberId(member.getNumberId());
            ruleScore.setUnitPost(member.getUnitPost());
            ruleScore.setBasicScore(0);
            ruleScore.setRewardScore(0);
            ruleScore.setTotalScore(0);
            ruleScore.setScoreDetail(ruleInfoJson);
            ruleScoreList.add(ruleScore);
        }

        log.info("修复版原始计算完成，数据量: {}", ruleScoreList.size());
        return ruleScoreList;
    }

    /**
     * 可工作的原始计算逻辑 - 完全按照最初的方式，只修复深拷贝问题
     */
    private List<RuleScore> calculateScoreOriginalWorking(List<CommitteeMember> memberList, RuleInfoVo ruleInfo,
                                                         TripleMap<String, String, String> strategyMap) {
        List<RuleScore> ruleScoreList = new ArrayList<>();

        for (CommitteeMember member : memberList) {
            Integer basicScore = 0;
            Integer rewardScore = 0;

            // 直接使用原始规则信息，避免深拷贝导致的strategyKey丢失
            for (RuleDetailVo ruleDetailVo : ruleInfo.getChildren()) {
                // 重置所有节点的finalScore，避免上次计算的影响
                resetFinalScores(ruleDetailVo);
                // 使用原始的评估方法
                evaluateLeafNodesOriginal(member, ruleDetailVo, strategyMap);

                // 保持原有的基于remark的判断逻辑
                if (ruleDetailVo.getRemark().equals("基础分")) {
                    basicScore += ruleDetailVo.getFinalScore() != null ? ruleDetailVo.getFinalScore() : 0;
                }
                if (ruleDetailVo.getRemark().equals("奖励分")) {
                    rewardScore += ruleDetailVo.getFinalScore() != null ? ruleDetailVo.getFinalScore() : 0;
                }
            }

            RuleScore ruleScore = new RuleScore();
            ruleScore.setId(member.getId());
            ruleScore.setYear(Integer.valueOf(member.getYear()));
            ruleScore.setUserId(Long.valueOf(member.getUserId()));
            ruleScore.setUserName(member.getUserName());
            ruleScore.setNumberId(member.getNumberId());
            ruleScore.setUnitPost(member.getUnitPost());
            ruleScore.setBasicScore(basicScore);
            ruleScore.setRewardScore(rewardScore);
            ruleScore.setTotalScore(basicScore + rewardScore);
            ruleScore.setScoreDetail(JSON.toJSONString(ruleInfo));
            ruleScoreList.add(ruleScore);
        }

        return ruleScoreList;
    }

    /**
     * 重置规则树中所有节点的finalScore
     */
    private void resetFinalScores(RuleDetailVo ruleDetail) {
        if (ruleDetail == null) return;

        ruleDetail.setFinalScore(null);

        if (ruleDetail.getChildren() != null) {
            for (RuleDetailVo child : ruleDetail.getChildren()) {
                resetFinalScores(child);
            }
        }
    }

    /**
     * 原始的叶子节点评估方法
     */
    private void evaluateLeafNodesOriginal(CommitteeMember member, RuleDetailVo ruleDetail,
                                         TripleMap<String, String, String> strategyMap) {
        if (ruleDetail == null) {
            log.debug("规则详情为空，跳过");
            return;
        }

        log.info("评估规则节点: {}, 策略键: {}, 是否有子节点: {}",
                 ruleDetail.getRemark(), ruleDetail.getStrategyKey(),
                 !ObjectUtil.isEmpty(ruleDetail.getChildren()));

        // 1. 如果是叶子节点：计算自身 finalScore
        if (ObjectUtil.isEmpty(ruleDetail.getChildren())) {
            if (ruleDetail.getStrategyKey() == null) {
                log.info("叶子节点策略键为空，跳过: {}", ruleDetail.getRemark());
                return;
            }

            Pair<String, String> strategyPair = strategyMap.get(ruleDetail.getStrategyKey());
            if (strategyPair == null) {
                log.warn("未找到策略映射，策略键: {}", ruleDetail.getStrategyKey());
                return;
            }

            log.info("执行策略: 委员={}, 规则={}, 策略键={}, 策略类型={}, 方法={}, 规则分值={}",
                     member.getUserName(), ruleDetail.getRemark(), ruleDetail.getStrategyKey(),
                     strategyPair.getKey(), strategyPair.getValue(), ruleDetail.getScore());

            try {
                Integer score = calculateScoreByStrategyOriginal(member, ruleDetail, strategyPair);
                ruleDetail.setFinalScore(score);
                log.info("策略执行完成: 委员={}, 规则={}, 分数={}, 执行前分值={}, 执行后finalScore={}",
                         member.getUserName(), ruleDetail.getRemark(), score,
                         ruleDetail.getScore(), ruleDetail.getFinalScore());
            } catch (Exception e) {
                log.error("Execute Method[{}] failed for member {}: ",
                         strategyPair.getValue(), member.getUserName(), e);
            }
        } else {
            int childrenTotalScore = 0;
            for (RuleDetailVo child : ruleDetail.getChildren()) {
                evaluateLeafNodesOriginal(member, child, strategyMap);
                Integer childScore = child.getFinalScore() != null ? child.getFinalScore() : 0;
                childrenTotalScore += childScore;
                log.debug("子节点分数汇总: 父节点={}, 子节点={}, 子节点分数={}, 累计分数={}",
                         ruleDetail.getRemark(), child.getRemark(), childScore, childrenTotalScore);
            }
            ruleDetail.setFinalScore(childrenTotalScore);
            log.debug("非叶子节点分数设置完成: 节点={}, 总分={}", ruleDetail.getRemark(), childrenTotalScore);
        }
    }

    /**
     * 原始的策略计算方法
     */
    private Integer calculateScoreByStrategyOriginal(CommitteeMember member, RuleDetailVo ruleDetail,
                                                   Pair<String, String> strategyPair) throws Exception {
        String ruleType = strategyPair.getKey();
        String methodName = strategyPair.getValue();

        log.debug("开始执行策略计算: 委员={}, 规则={}, 策略类型={}, 方法名={}",
                 member.getUserName(), ruleDetail.getRemark(), ruleType, methodName);

        if (methodName == null) {
            log.warn("方法名为空，返回0分");
            return 0;
        }

        switch (ruleType) {
            case "BASIC":
                log.debug("执行基础策略: {}", methodName);
                Method basicMethod = this.basicStrategy.getClass().getMethod(methodName, CommitteeMember.class, RuleDetailVo.class);
                log.debug("调用方法: {}", basicMethod.getName());
                basicMethod.invoke(this.basicStrategy, member, ruleDetail);
                Integer basicScore = ruleDetail.getFinalScore();
                log.debug("基础策略执行完成: 委员={}, 方法={}, 分数={}",
                         member.getUserName(), methodName, basicScore);
                return basicScore;
            case "REWARD":
                log.debug("执行奖励策略: {}", methodName);
                Method rewardMethod = this.rewardStrategy.getClass().getMethod(methodName, CommitteeMember.class, RuleDetailVo.class);
                log.debug("调用方法: {}", rewardMethod.getName());
                rewardMethod.invoke(this.rewardStrategy, member, ruleDetail);
                Integer rewardScore = ruleDetail.getFinalScore();
                log.debug("奖励策略执行完成: 委员={}, 方法={}, 分数={}",
                         member.getUserName(), methodName, rewardScore);
                return rewardScore;
            default:
                log.warn("未知策略类型: {}", ruleType);
                return 0;
        }
    }

    /**
     * 计算单个委员的分数
     */
    private RuleScore calculateMemberScore(CommitteeMember member, RuleInfoVo ruleInfo,
                                         Map<String, BiFunction<CommitteeMember, RuleDetailVo, Integer>> strategyMethodMap,
                                         String ruleInfoJson) {
        log.debug("开始计算委员分数: {}", member.getUserName());

        // 深拷贝规则信息，避免并发修改
        RuleInfoVo memberRuleInfo = JSON.parseObject(JSON.toJSONString(ruleInfo), RuleInfoVo.class);

        Integer basicScore = 0;
        Integer rewardScore = 0;

        if (memberRuleInfo.getChildren() != null) {
            for (RuleDetailVo ruleDetailVo : memberRuleInfo.getChildren()) {
                log.debug("处理规则: {}", ruleDetailVo.getRemark());
                evaluateLeafNodesOptimized(member, ruleDetailVo, strategyMethodMap);

                Integer finalScore = Optional.ofNullable(ruleDetailVo.getFinalScore()).orElse(0);
                log.debug("规则 {} 最终分数: {}", ruleDetailVo.getRemark(), finalScore);

                // 使用常量比较，提高效率
                if (BASIC_SCORE_TYPE.equals(ruleDetailVo.getRemark())) {
                    basicScore += finalScore;
                } else if (REWARD_SCORE_TYPE.equals(ruleDetailVo.getRemark())) {
                    rewardScore += finalScore;
                }
            }
        }

        log.info("委员 {} 分数计算完成: 基础分={}, 奖励分={}, 总分={}",
                member.getUserName(), basicScore, rewardScore, basicScore + rewardScore);

        // 构建结果对象
        RuleScore ruleScore = new RuleScore();
        ruleScore.setId(member.getId());
        ruleScore.setYear(Integer.valueOf(member.getYear()));
        ruleScore.setUserId(Long.valueOf(member.getUserId()));
        ruleScore.setUserName(member.getUserName());
        ruleScore.setNumberId(member.getNumberId());
        ruleScore.setUnitPost(member.getUnitPost());
        ruleScore.setBasicScore(basicScore);
        ruleScore.setRewardScore(rewardScore);
        ruleScore.setTotalScore(basicScore + rewardScore);
        ruleScore.setScoreDetail(ruleInfoJson);

        return ruleScore;
    }

    /**
     * 构建策略方法映射，预编译反射方法
     */
    private Map<String, BiFunction<CommitteeMember, RuleDetailVo, Integer>> buildStrategyMethodMap(
            TripleMap<String, String, String> strategyMap) {
        Map<String, BiFunction<CommitteeMember, RuleDetailVo, Integer>> methodMap = new HashMap<>();

        for (String strategyKey : strategyMap.keySet()) {
            Pair<String, String> strategyPair = strategyMap.get(strategyKey);
            if (strategyPair == null) {
                log.warn("策略对为空，strategyKey: {}", strategyKey);
                continue;
            }

            String ruleType = strategyPair.getKey();
            String methodName = strategyPair.getValue();

            // 移除调试日志以提升性能

            if (methodName == null) {
                log.warn("方法名为空，strategyKey: {}, ruleType: {}", strategyKey, ruleType);
                continue;
            }

            try {
                Method method = getOrCacheMethod(ruleType, methodName);
                BiFunction<CommitteeMember, RuleDetailVo, Integer> function =
                    createStrategyFunction(ruleType, method);
                methodMap.put(strategyKey, function);
                // 成功构建策略方法
            } catch (Exception e) {
                log.error("Failed to build strategy method for key: {}, method: {}",
                         strategyKey, methodName, e);
            }
        }

        return methodMap;
    }

    /**
     * 获取或缓存反射方法
     */
    private Method getOrCacheMethod(String ruleType, String methodName) throws NoSuchMethodException {
        String cacheKey = ruleType + ":" + methodName;
        return methodCache.computeIfAbsent(cacheKey, key -> {
            try {
                if (STRATEGY_TYPE_BASIC.equals(ruleType)) {
                    return basicStrategy.getClass().getMethod(methodName,
                           CommitteeMember.class, RuleDetailVo.class);
                } else if (STRATEGY_TYPE_REWARD.equals(ruleType)) {
                    return rewardStrategy.getClass().getMethod(methodName,
                           CommitteeMember.class, RuleDetailVo.class);
                }
                throw new IllegalArgumentException("Unknown strategy type: " + ruleType);
            } catch (NoSuchMethodException e) {
                throw new RuntimeException("Method not found: " + methodName, e);
            }
        });
    }

    /**
     * 创建策略执行函数
     */
    private BiFunction<CommitteeMember, RuleDetailVo, Integer> createStrategyFunction(
            String ruleType, Method method) {
        return (member, ruleDetail) -> {
            try {
                if (STRATEGY_TYPE_BASIC.equals(ruleType)) {
                    method.invoke(basicStrategy, member, ruleDetail);
                } else if (STRATEGY_TYPE_REWARD.equals(ruleType)) {
                    method.invoke(rewardStrategy, member, ruleDetail);
                }
                return Optional.ofNullable(ruleDetail.getFinalScore()).orElse(0);
            } catch (Exception e) {
                log.error("Strategy execution failed for method: {}", method.getName(), e);
                return 0;
            }
        };
    }

    /**
     * 优化后的叶子节点评估方法，暂时回退到递归实现确保正确性
     */
    private void evaluateLeafNodesOptimized(CommitteeMember member, RuleDetailVo ruleDetail,
                                          Map<String, BiFunction<CommitteeMember, RuleDetailVo, Integer>> strategyMethodMap) {
        if (ruleDetail == null) {
            return;
        }

        // 1. 如果是叶子节点：计算自身 finalScore
        if (ObjectUtil.isEmpty(ruleDetail.getChildren())) {
            calculateLeafNodeScore(member, ruleDetail, strategyMethodMap);
        } else {
            // 2. 如果是非叶子节点：先递归计算所有子节点，然后汇总
            int childrenTotalScore = 0;
            for (RuleDetailVo child : ruleDetail.getChildren()) {
                evaluateLeafNodesOptimized(member, child, strategyMethodMap);
                childrenTotalScore += Optional.ofNullable(child.getFinalScore()).orElse(0);
            }
            ruleDetail.setFinalScore(childrenTotalScore);
        }
    }

    /**
     * 计算叶子节点分数
     */
    private void calculateLeafNodeScore(CommitteeMember member, RuleDetailVo ruleDetail,
                                      Map<String, BiFunction<CommitteeMember, RuleDetailVo, Integer>> strategyMethodMap) {
        if (ruleDetail.getStrategyKey() == null) {
            log.debug("策略键为空，设置分数为0，规则: {}", ruleDetail.getRemark());
            ruleDetail.setFinalScore(0);
            return;
        }

        BiFunction<CommitteeMember, RuleDetailVo, Integer> strategyFunction =
            strategyMethodMap.get(ruleDetail.getStrategyKey());

        if (strategyFunction == null) {
            log.warn("未找到策略函数，strategyKey: {}, 设置分数为0", ruleDetail.getStrategyKey());
            ruleDetail.setFinalScore(0);
            return;
        }

        try {
            Integer score = strategyFunction.apply(member, ruleDetail);
            ruleDetail.setFinalScore(score);
            log.debug("计算叶子节点分数成功: 委员={}, 规则={}, 策略={}, 分数={}",
                     member.getUserName(), ruleDetail.getRemark(), ruleDetail.getStrategyKey(), score);
        } catch (Exception e) {
            log.error("Strategy execution failed for key: {}, member: {}",
                     ruleDetail.getStrategyKey(), member.getUserName(), e);
            ruleDetail.setFinalScore(0);
        }
    }


    /**
     * @deprecated 已被优化的策略方法映射替代
     * 保留此方法以防需要回退
     */
    @Deprecated
    private Integer calculateScoreByStrategy(CommitteeMember member, RuleDetailVo ruleDetail,
                                             Pair<String, String> strategyPair) throws Exception {
        String ruleType = strategyPair.getKey();
        String methodName = strategyPair.getValue();

        if (methodName == null) {
            return 0;
        }

        switch (ruleType) {
            case "BASIC":
                Method basicMethod = getOrCacheMethod(STRATEGY_TYPE_BASIC, methodName);
                basicMethod.invoke(basicStrategy, member, ruleDetail);
                return ruleDetail.getFinalScore();
            case "REWARD":
                Method rewardMethod = getOrCacheMethod(STRATEGY_TYPE_REWARD, methodName);
                rewardMethod.invoke(rewardStrategy, member, ruleDetail);
                return ruleDetail.getFinalScore();
            default:
                log.warn("未知策略类型: {}", ruleType);
                return 0;
        }
    }

    private Integer upsertRuleScoresBatch(List<RuleScore> ruleScoreList) {
        if (ObjectUtil.isEmpty(ruleScoreList)) {
            return 0;
        }

        log.info("开始批量保存规则分数，数据量: {}", ruleScoreList.size());

        // 检查数据唯一性，避免重复插入
        Set<Long> idSet = new HashSet<>();
        List<RuleScore> uniqueList = new ArrayList<>();
        Map<Long, Integer> duplicateCount = new HashMap<>();

        for (RuleScore score : ruleScoreList) {
            if (idSet.add(score.getId())) {
                uniqueList.add(score);
            } else {
                duplicateCount.put(score.getId(), duplicateCount.getOrDefault(score.getId(), 1) + 1);
                log.warn("发现重复的委员ID: {}, 姓名: {}, 重复次数: {}",
                        score.getId(), score.getUserName(), duplicateCount.get(score.getId()));
            }
        }

        if (uniqueList.size() != ruleScoreList.size()) {
            log.warn("去重后数据量: {} -> {}, 重复委员数: {}",
                    ruleScoreList.size(), uniqueList.size(), duplicateCount.size());
            duplicateCount.forEach((id, count) ->
                log.warn("委员ID {} 重复了 {} 次", id, count));
        }

        // 输出前几个委员的详细信息用于调试
        uniqueList.stream().limit(3).forEach(score ->
            log.info("委员详情: ID={}, 姓名={}, 基础分={}, 奖励分={}, 总分={}",
                    score.getId(), score.getUserName(), score.getBasicScore(),
                    score.getRewardScore(), score.getTotalScore()));

        log.info("实际处理数据量: {}", uniqueList.size());

        try {
            // 使用简单的批处理，避免复杂的并行逻辑
            int batchSize = 50;
            int totalAffected = 0;

            List<List<RuleScore>> batches = Lists.partition(uniqueList, batchSize);
            log.info("分批处理，批次数: {}, 每批大小: {}", batches.size(), batchSize);

            for (int i = 0; i < batches.size(); i++) {
                List<RuleScore> batch = batches.get(i);
                try {
                    int affected = ruleScoreMapper.upsertRuleScoresBatch(batch);
                    // MySQL的ON DUPLICATE KEY UPDATE影响行数规则：
                    // INSERT = 1, UPDATE = 2，所以我们需要计算实际处理的记录数
                    int actualRecords = Math.min(affected, batch.size() * 2); // 最多是批次大小的2倍
                    int processedRecords = (affected > batch.size()) ? batch.size() : affected; // 实际处理的记录数

                    totalAffected += processedRecords;
                    log.debug("批次 {}/{} 完成，MySQL影响行数: {}, 实际处理记录数: {}",
                             i + 1, batches.size(), affected, processedRecords);
                } catch (Exception e) {
                    log.error("批次 {}/{} 失败，批次大小: {}", i + 1, batches.size(), batch.size(), e);
                    // 单条重试，但要避免重复计数
                    for (RuleScore item : batch) {
                        try {
                            int singleAffected = ruleScoreMapper.upsertRuleScoresBatch(Collections.singletonList(item));
                            int singleProcessed = (singleAffected > 1) ? 1 : singleAffected;
                            totalAffected += singleProcessed;
                        } catch (Exception singleE) {
                            log.error("单条插入失败，委员: {} (ID: {})", item.getUserName(), item.getId(), singleE);
                        }
                    }
                }
            }

            log.info("批量保存完成，总影响行数: {}", totalAffected);
            return totalAffected;

        } catch (Exception e) {
            log.error("批量保存失败", e);
            throw new ServiceException("批量保存规则分数失败: " + e.getMessage());
        }
    }
}
