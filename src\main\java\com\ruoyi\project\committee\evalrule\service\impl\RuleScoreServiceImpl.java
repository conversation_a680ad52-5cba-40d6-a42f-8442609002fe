package com.ruoyi.project.committee.evalrule.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.collections.TripleMap;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.project.committee.archive.domain.CommitteeMember;
import com.ruoyi.project.committee.archive.mapper.CommitteeMemberMapper;
import com.ruoyi.project.committee.evalrule.domain.RuleScore;
import com.ruoyi.project.committee.evalrule.domain.dto.RuleScorePageDto;
import com.ruoyi.project.committee.evalrule.domain.vo.RuleDetailVo;
import com.ruoyi.project.committee.evalrule.domain.vo.RuleInfoVo;
import com.ruoyi.project.committee.evalrule.mapper.RuleScoreMapper;
import com.ruoyi.project.committee.evalrule.service.IRuleInfoService;
import com.ruoyi.project.committee.evalrule.service.IRuleScoreService;
import com.ruoyi.project.committee.evalrule.service.IRuleStrategyService;
import com.ruoyi.project.committee.evalrule.strategy.BasicStrategy;
import com.ruoyi.project.committee.evalrule.strategy.RewardStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RuleScoreServiceImpl extends ServiceImpl<RuleScoreMapper, RuleScore> implements IRuleScoreService {

    @Resource
    private RuleScoreMapper ruleScoreMapper;

    @Resource
    private IRuleStrategyService ruleStrategyService;

    @Resource
    private IRuleInfoService ruleInfoService;

    @Resource
    private CommitteeMemberMapper committeeMemberMapper;


    @Override
    public IPage<RuleScore> selectRuleScorePage(RuleScorePageDto pageDto) {
        if (ObjectUtil.isNull(pageDto.getYear())) {
            pageDto.setYear(DateUtil.thisYear());
        }
        Page<RuleScore> page = new Page<>(pageDto.getCurrentPage(), pageDto.getPageSize());
        return ruleScoreMapper.selectRuleScorePage(page, pageDto);
    }

    @Override
    public RuleScore getById(String id) {
        return ruleScoreMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    // TODO-calculateScore
    public Integer calculateScore(Integer year) {
        // 获取委员
        List<CommitteeMember> memberList = committeeMemberMapper.selectMemberByYear(year);
        if (ObjectUtil.isEmpty(memberList)) {
            throw new ServiceException("未找到" + year + "年度的委员信息.");
        }

        // 获取规则
        RuleInfoVo ruleInfo = ruleInfoService.selectRuleInfoByYear(year);
        if (ObjectUtil.isEmpty(ruleInfo)) {
            throw new ServiceException("未找到" + year + "年度的规则信息.");
        }

        // 获取策略映射
        TripleMap<String, String, String> strategyMap = ruleStrategyService.getStrategyMap();

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("计算规则分数");
        List<RuleScore> ruleScoreList = new ArrayList<>();
        for (CommitteeMember member : memberList) {
            Integer basicScore = 0;
            Integer rewardScore = 0;
            for (RuleDetailVo ruleDetailVo : ruleInfo.getChildren()) {
                evaluateLeafNodes(member, ruleDetailVo, strategyMap);
                if (ruleDetailVo.getRemark().equals("基础分")) {
                    basicScore += ruleDetailVo.getFinalScore();
                }
                if (ruleDetailVo.getRemark().equals("奖励分")) {
                    rewardScore += ruleDetailVo.getFinalScore();
                }
            }
            RuleScore ruleScore = new RuleScore();
            ruleScore.setId(member.getId());
            ruleScore.setYear(Integer.valueOf(member.getYear()));
            ruleScore.setUserId(Long.valueOf(member.getUserId()));
            ruleScore.setUserName(member.getUserName());
            ruleScore.setNumberId(member.getNumberId());
            ruleScore.setUnitPost(member.getUnitPost());
            ruleScore.setBasicScore(basicScore);
            ruleScore.setRewardScore(rewardScore);
            ruleScore.setTotalScore(basicScore + rewardScore);
            ruleScore.setScoreDetail(JSON.toJSONString(ruleInfo));
            ruleScoreList.add(ruleScore);
        }

        stopWatch.stop();
        log.info("计算规则分数完成，耗时：{} ms", stopWatch.getTotalTimeMillis());


        // 暂时关闭
        stopWatch.start("保存规则分数");
        Integer affectRows = upsertRuleScoresBatch(ruleScoreList);
        stopWatch.stop();
        log.info("保存规则分数完成，耗时：{} ms", stopWatch.getTotalTimeMillis());

        return affectRows;
    }

    private void evaluateLeafNodes(CommitteeMember member, RuleDetailVo ruleDetail,
                                            TripleMap<String, String, String> strategyMap) {
        if (ruleDetail == null) {
            return;
        }

        // 1. 如果是叶子节点：计算自身 finalScore
        if (ObjectUtil.isEmpty(ruleDetail.getChildren())) {
            if (ruleDetail.getStrategyKey() == null) {
                return;
            }

            Pair<String, String> strategyPair = strategyMap.get(ruleDetail.getStrategyKey());
            if (strategyPair == null) {
                return;
            }

            try {
                Integer score = calculateScoreByStrategy(member, ruleDetail, strategyPair);
                ruleDetail.setFinalScore(score);
            } catch (Exception e) {
                log.error("Execute Method[{}] failed!: ", strategyPair.getValue(), e);
            }
        } else {
            int childrenTotalScore = 0;
            for (RuleDetailVo child : ruleDetail.getChildren()) {
                evaluateLeafNodes(member, child, strategyMap);
                childrenTotalScore += child.getFinalScore() != null ? child.getFinalScore() : 0;
            }
            ruleDetail.setFinalScore(childrenTotalScore);
        }
    }


    private Integer calculateScoreByStrategy(CommitteeMember member, RuleDetailVo ruleDetail,
                                             Pair<String, String> strategyPair) throws Exception {
        String ruleType = strategyPair.getKey();
        String methodName = strategyPair.getValue();

        if (methodName == null) {
            return 0;
        }

        switch (ruleType) {
            case "BASIC":
                BasicStrategy basicStrategy = SpringUtils.getBean(BasicStrategy.class);
                Method basicMethod = basicStrategy.getClass().getMethod(methodName, CommitteeMember.class, RuleDetailVo.class);
                basicMethod.invoke(basicStrategy, member, ruleDetail);
                return ruleDetail.getFinalScore();
            case "REWARD":
                RewardStrategy rewardStrategy = SpringUtils.getBean(RewardStrategy.class);
                Method rewardMethod = rewardStrategy.getClass().getMethod(methodName, CommitteeMember.class, RuleDetailVo.class);
                rewardMethod.invoke(rewardStrategy, member, ruleDetail);
                return ruleDetail.getFinalScore();
            default:
                log.warn("未知策略类型: {}", ruleType);
                return 0;
        }
    }

    private Integer upsertRuleScoresBatch(List<RuleScore> ruleScoreList) {
        if (ObjectUtil.isEmpty(ruleScoreList)) {
            return 0;
        }

        // 1. 计算安全并行度
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        int safeParallelism = Math.min(4, Math.max(2, availableProcessors > 0 ? availableProcessors - 1 : 1));

        // 3. 创建线程池（添加线程池名称便于监控）
        ForkJoinPool privatePool = new ForkJoinPool(safeParallelism,
                ForkJoinPool.defaultForkJoinWorkerThreadFactory,
                null,
                false
        );

        try {
            // 4. 动态批次大小（基于数据量和并行度）
            int batchSize = Math.min(50, ruleScoreList.size() / safeParallelism + 1);

            // 5. 执行并行处理（显式处理sum结果）
            return privatePool.submit(() ->
                    Lists.partition(ruleScoreList, batchSize)
                            .parallelStream()
                            .mapToInt(batch -> {
                                try {
                                    return ruleScoreMapper.upsertRuleScoresBatch(batch);
                                } catch (Exception e) {
                                    log.error("Batch failed (size={}, firstId={})",
                                            batch.size(), batch.get(0).getId(), e);
                                    return batch.stream()
                                            .mapToInt(item -> ruleScoreMapper.upsertRuleScoresBatch(
                                                    Collections.singletonList(item)
                                            ))
                                            .sum();
                                }
                            })
                            .sum()
            ).get(30, TimeUnit.SECONDS);

        } catch (TimeoutException e) {
            log.error("Batch timeout after 30s, dataSize={}", ruleScoreList.size(), e);
            throw new ServiceException("BATCH_PROCESS_TIMEOUT");
        } catch (Exception e) {
            log.error("Unexpected batch error, parallelism={}", safeParallelism, e);
            throw new ServiceException("BATCH_PROCESS_FAILED");
        } finally {
            // 6. 强制关闭线程池（终止等待）
            privatePool.shutdown();
            try {
                if (!privatePool.awaitTermination(5, TimeUnit.SECONDS)) {
                    privatePool.shutdownNow();
                }
            } catch (InterruptedException e) {
                privatePool.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
